# SSL Certificate Setup Guide

## 🔒 SSL Certificate Configuration for New-API

This guide covers SSL certificate setup for your New-API deployment with domain configuration.

## 📋 Prerequisites

- Domain name pointing to your server
- Nginx installed and configured
- Port 80 and 443 open in firewall

## 🚀 Automatic SSL Setup (Recommended)

### Using Certbot with Nginx

```bash
# Install Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Generate SSL certificate for your domain
sudo certbot --nginx -d your-domain.com -d api.your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

### Certificate Auto-Renewal

Certbot automatically sets up a cron job for renewal. Verify it:

```bash
# Check renewal timer
sudo systemctl status certbot.timer

# Manual renewal test
sudo certbot renew --dry-run
```

## 🔧 Manual SSL Configuration

### 1. Generate Certificate

```bash
# Generate certificate only (without nginx auto-config)
sudo certbot certonly --nginx -d your-domain.com -d api.your-domain.com
```

### 2. Update Nginx Configuration

Edit your nginx configuration file:

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com api.your-domain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Your location blocks here...
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name your-domain.com api.your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

### 3. Test and Reload

```bash
# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

## 🔄 Migration SSL Setup

When migrating to a new server:

### 1. On New Server

```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Generate new certificates
sudo certbot --nginx -d your-domain.com -d api.your-domain.com
```

### 2. DNS Update

Update your domain's DNS records to point to the new server IP address.

### 3. Verify SSL

```bash
# Test SSL certificate
openssl s_client -connect your-domain.com:443 -servername your-domain.com

# Check certificate expiration
echo | openssl s_client -connect your-domain.com:443 2>/dev/null | openssl x509 -noout -dates
```

## 🛠️ Troubleshooting

### Certificate Generation Failed

```bash
# Check if port 80 is accessible
sudo netstat -tlnp | grep :80

# Check nginx status
sudo systemctl status nginx

# Check domain DNS resolution
nslookup your-domain.com
```

### Certificate Renewal Issues

```bash
# Check renewal logs
sudo journalctl -u certbot.timer

# Manual renewal with verbose output
sudo certbot renew --verbose

# Force renewal (if needed)
sudo certbot renew --force-renewal
```

### Nginx SSL Errors

```bash
# Check nginx error logs
sudo tail -f /var/log/nginx/error.log

# Verify certificate files exist
sudo ls -la /etc/letsencrypt/live/your-domain.com/

# Test SSL configuration
sudo nginx -t
```

## 📁 Certificate File Locations

Let's Encrypt certificates are stored in:

```
/etc/letsencrypt/live/your-domain.com/
├── cert.pem          # Certificate only
├── chain.pem         # Certificate chain
├── fullchain.pem     # Certificate + chain (use this)
└── privkey.pem       # Private key
```

## 🔄 Certificate Backup

For migration purposes, you can backup certificates:

```bash
# Backup entire letsencrypt directory
sudo tar -czf letsencrypt-backup.tar.gz /etc/letsencrypt/

# Restore on new server (not recommended, better to generate new)
sudo tar -xzf letsencrypt-backup.tar.gz -C /
```

**Note**: It's better to generate new certificates on the new server rather than copying old ones.

## ⚡ Quick Commands

```bash
# Check certificate status
sudo certbot certificates

# Renew all certificates
sudo certbot renew

# Revoke a certificate
sudo certbot revoke --cert-path /etc/letsencrypt/live/your-domain.com/cert.pem

# Delete a certificate
sudo certbot delete --cert-name your-domain.com
```

## 🔐 Security Best Practices

1. **Use strong SSL protocols**: TLSv1.2 and TLSv1.3 only
2. **Enable HSTS**: Add Strict-Transport-Security header
3. **Use secure ciphers**: Modern cipher suites only
4. **Regular updates**: Keep certbot and nginx updated
5. **Monitor expiration**: Set up alerts for certificate expiration

## 📞 Support

If you encounter issues:

1. Check nginx error logs: `sudo tail -f /var/log/nginx/error.log`
2. Check certbot logs: `sudo journalctl -u certbot.timer`
3. Verify domain DNS: `nslookup your-domain.com`
4. Test port accessibility: `telnet your-domain.com 80`
