# New-API 开机自动启动设置指南

## 方法一：使用自动安装脚本（推荐）

1. 运行自动安装脚本：
```bash
./setup-autostart.sh
```

2. 如果需要立即启动服务：
```bash
sudo systemctl start new-api
```

## 方法二：手动设置

### 1. 复制服务文件
```bash
sudo cp new-api.service /etc/systemd/system/
sudo chmod 644 /etc/systemd/system/new-api.service
```

### 2. 重新加载systemd并启用服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable new-api.service
```

### 3. 启动服务（可选）
```bash
sudo systemctl start new-api.service
```

## 服务管理命令

- **启动服务**：`sudo systemctl start new-api`
- **停止服务**：`sudo systemctl stop new-api`
- **重启服务**：`sudo systemctl restart new-api`
- **查看状态**：`sudo systemctl status new-api`
- **查看日志**：`sudo journalctl -u new-api -f`

## 禁用自动启动

如果不再需要开机自动启动：
```bash
sudo systemctl disable new-api
sudo systemctl stop new-api
```

## 完全移除服务

```bash
sudo systemctl disable new-api
sudo systemctl stop new-api
sudo rm /etc/systemd/system/new-api.service
sudo systemctl daemon-reload
```

## 注意事项

1. **用户权限**：服务配置为使用 `loongson` 用户运行，确保该用户有权限访问Docker
2. **Docker依赖**：服务会等待Docker服务启动后再启动
3. **网络依赖**：服务会等待网络连接就绪后启动
4. **工作目录**：服务工作目录设置为 `/home/<USER>/share/new-api`

## 故障排除

### 查看服务状态
```bash
sudo systemctl status new-api
```

### 查看详细日志
```bash
sudo journalctl -u new-api -n 50
```

### 检查Docker是否运行
```bash
sudo systemctl status docker
```

### 验证用户权限
```bash
# 确保用户在docker组中
groups $USER
# 如果没有，添加用户到docker组
sudo usermod -aG docker $USER
# 然后重新登录或重启
```
