# New-API 自动更新配置指南

## 📋 概述

本指南将帮助您配置 New-API 项目的自动更新功能，支持多种更新策略，确保您的服务始终运行最新版本。

## 🔄 自动更新方案

### 方案一：Watchtower 自动更新（推荐）

Watchtower 是一个轻量级的 Docker 容器，可以自动监控和更新您的 Docker 镜像。

#### 1. 快速设置

```bash
# 运行自动配置脚本
sudo ./auto-update-setup.sh
```

#### 2. 手动配置

如果您想手动配置，可以使用提供的配置文件：

```bash
# 备份当前配置
cp docker-compose.yml docker-compose.yml.backup

# 使用带自动更新的配置
cp docker-compose.auto-update.yml docker-compose.yml

# 重启服务
docker compose down && docker compose up -d
```

#### 3. Watchtower 配置说明

| 环境变量 | 说明 | 默认值 |
|---------|------|--------|
| `WATCHTOWER_POLL_INTERVAL` | 检查更新间隔（秒） | 3600 (1小时) |
| `WATCHTOWER_CLEANUP` | 清理旧镜像 | true |
| `WATCHTOWER_LABEL_ENABLE` | 只更新有标签的容器 | true |
| `WATCHTOWER_ROLLING_RESTART` | 滚动重启 | true |

### 方案二：定时任务更新

#### 1. 创建更新脚本

```bash
# 使用提供的手动更新脚本
./update-new-api.sh
```

#### 2. 设置 Cron 定时任务

```bash
# 编辑 crontab
sudo crontab -e

# 添加以下行（每天凌晨2点更新）
0 2 * * * cd /root/new-api && ./update-new-api.sh >> /var/log/new-api-update.log 2>&1
```

### 方案三：GitHub Actions 自动部署

如果您 fork 了项目，可以配置 GitHub Actions 自动部署：

```yaml
# .github/workflows/auto-deploy.yml
name: Auto Deploy
on:
  schedule:
    - cron: '0 2 * * *'  # 每天凌晨2点
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.KEY }}
          script: |
            cd /root/new-api
            docker compose pull
            docker compose up -d
```

## ⚙️ 配置选项

### 1. 更新频率配置

```yaml
# 每小时检查
- WATCHTOWER_POLL_INTERVAL=3600

# 每6小时检查
- WATCHTOWER_POLL_INTERVAL=21600

# 每天检查
- WATCHTOWER_POLL_INTERVAL=86400
```

### 2. 通知配置

#### 邮件通知

```yaml
environment:
  - WATCHTOWER_NOTIFICATIONS=email
  - WATCHTOWER_NOTIFICATION_EMAIL_FROM=<EMAIL>
  - WATCHTOWER_NOTIFICATION_EMAIL_TO=<EMAIL>
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER=smtp.gmail.com
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PORT=587
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_USER=<EMAIL>
  - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PASSWORD=your-app-password
```

#### Webhook 通知

```yaml
environment:
  - WATCHTOWER_NOTIFICATIONS=slack
  - WATCHTOWER_NOTIFICATION_SLACK_HOOK_URL=https://hooks.slack.com/services/xxx
```

### 3. 安全配置

```yaml
# 只更新特定容器
labels:
  - "com.centurylinklabs.watchtower.enable=true"

# 排除特定容器
labels:
  - "com.centurylinklabs.watchtower.enable=false"
```

## 🛠️ 管理命令

### 查看更新状态

```bash
# 查看 Watchtower 日志
docker logs watchtower

# 查看服务状态
docker compose ps

# 查看当前版本
curl -s http://localhost:3000/api/status | jq '.data.version'
```

### 手动触发更新

```bash
# 手动更新
./update-new-api.sh

# 强制重新拉取镜像
docker compose pull && docker compose up -d
```

### 回滚操作

```bash
# 查看镜像历史
docker images calciumion/new-api

# 回滚到指定版本
docker tag calciumion/new-api:v1.0.0 calciumion/new-api:latest
docker compose up -d
```

## 📊 监控和日志

### 1. 更新日志

```bash
# Watchtower 日志
docker logs watchtower -f

# 系统更新日志
tail -f /var/log/new-api-auto-update.log
```

### 2. 健康检查

```bash
# 检查服务健康状态
docker compose ps
curl -f http://localhost:3000/api/status || echo "Service unhealthy"
```

### 3. 监控脚本

```bash
#!/bin/bash
# monitor-updates.sh

# 检查服务状态
if ! curl -f http://localhost:3000/api/status > /dev/null 2>&1; then
    echo "$(date): Service is down, attempting restart..." >> /var/log/new-api-monitor.log
    docker compose restart new-api
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage high ($DISK_USAGE%), cleaning up..." >> /var/log/new-api-monitor.log
    docker system prune -f
fi
```

## ⚠️ 注意事项

1. **数据备份**：自动更新前建议备份数据库和配置文件
2. **测试环境**：建议先在测试环境验证更新
3. **回滚计划**：准备回滚方案以防更新失败
4. **监控告警**：配置监控确保更新后服务正常
5. **网络要求**：确保服务器能访问 Docker Hub

## 🔧 故障排除

### 常见问题

1. **Watchtower 无法拉取镜像**
   ```bash
   # 检查网络连接
   docker pull calciumion/new-api:latest
   
   # 检查 Docker Hub 访问
   curl -I https://registry-1.docker.io/
   ```

2. **更新后服务无法启动**
   ```bash
   # 查看容器日志
   docker logs new-api
   
   # 回滚到上一版本
   docker compose down
   docker tag calciumion/new-api:backup calciumion/new-api:latest
   docker compose up -d
   ```

3. **数据库迁移问题**
   ```bash
   # 检查数据库连接
   docker exec -it mysql mysql -u root -p123456 -e "SHOW DATABASES;"
   
   # 查看迁移日志
   docker logs new-api | grep -i migration
   ```

## 📞 支持

如果遇到问题，请：

1. 查看项目文档：https://docs.newapi.pro/
2. 检查 GitHub Issues：https://github.com/Calcium-Ion/new-api/issues
3. 查看日志文件获取详细错误信息
