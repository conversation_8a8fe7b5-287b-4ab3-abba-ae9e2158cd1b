#!/bin/bash

# 部署情侣网站到 https://liangliangdamowang.edu.deal/love
# 作者: AI Assistant
# 日期: $(date)

echo "🚀 开始部署情侣网站..."

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 错误: 请在 new-api 项目根目录下运行此脚本"
    exit 1
fi

# 检查情侣网站文件是否存在
if [ ! -f "../lover/index.html" ]; then
    echo "❌ 错误: 找不到情侣网站文件，请确保 lover 目录与 new-api 目录在同一级别"
    exit 1
fi

echo "📋 检查当前服务状态..."
docker compose ps

echo "🔄 停止现有服务..."
docker compose down

echo "🏗️ 重新构建并启动服务..."
docker compose up -d

echo "⏳ 等待服务启动..."
sleep 15

echo "🔍 检查服务状态..."
docker compose ps

echo "🧪 测试服务健康状态..."

# 测试nginx是否正常运行
if docker compose exec nginx nginx -t; then
    echo "✅ Nginx 配置测试通过"
else
    echo "❌ Nginx 配置测试失败"
    exit 1
fi

# 测试new-api服务是否正常
echo "🔍 测试 new-api 服务..."
sleep 5
if curl -f http://localhost/api/status > /dev/null 2>&1; then
    echo "✅ new-api 服务正常"
else
    echo "⚠️ new-api 服务可能还在启动中，请稍后检查"
fi

# 测试情侣网站是否可访问
echo "🔍 测试情侣网站..."
if curl -f http://localhost/love/ > /dev/null 2>&1; then
    echo "✅ 情侣网站部署成功！"
    echo "🌐 您现在可以通过以下地址访问："
    echo "   - HTTP: http://liangliangdamowang.edu.deal/love"
    echo "   - HTTPS: https://liangliangdamowang.edu.deal/love"
else
    echo "❌ 情侣网站访问失败，请检查配置"
fi

echo ""
echo "📊 服务状态总览:"
docker compose ps

echo ""
echo "📝 查看日志命令:"
echo "   docker compose logs nginx"
echo "   docker compose logs new-api"

echo ""
echo "🎉 部署完成！"
echo "💕 祝您和您的爱人幸福美满！"
