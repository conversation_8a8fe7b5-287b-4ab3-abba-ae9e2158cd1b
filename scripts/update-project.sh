#!/bin/bash

# New-API Project Safe Update Script
# 安全更新脚本 - 保护用户配置不被覆盖

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    New-API 安全更新系统                      ║"
    echo "║                                                              ║"
    echo "║  🔄 安全更新项目代码和脚本                                   ║"
    echo "║  🛡️ 保护用户配置和数据不被覆盖                              ║"
    echo "║  📦 自动备份重要文件                                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

print_step() {
    echo -e "${BLUE}[步骤 $1/6]${NC} ${GREEN}$2${NC}"
}

print_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "docker-compose.yml" ] || [ ! -f "cmd.sh" ]; then
        print_error "请在new-api项目根目录运行此脚本"
        exit 1
    fi
}

# 备份用户配置
backup_user_configs() {
    print_step 1 "备份用户配置文件"
    
    BACKUP_DIR="update_backup_$(TZ='Asia/Shanghai' date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 备份用户配置文件
    USER_CONFIGS=(
        ".env"
        "love-site/"
        "nginx/ssl/"
        "data/"
        "backup/"
        "logs/"
    )
    
    for config in "${USER_CONFIGS[@]}"; do
        if [ -e "$config" ]; then
            print_info "备份: $config"
            cp -r "$config" "$BACKUP_DIR/" 2>/dev/null || true
        fi
    done
    
    # 备份数据库
    if [ -f "scripts/backup-database.sh" ]; then
        print_info "创建数据库备份..."
        ./scripts/backup-database.sh || print_warning "数据库备份失败，但继续更新"
    fi
    
    echo "BACKUP_DIR=$BACKUP_DIR" > .update_backup_info
    print_info "配置备份完成: $BACKUP_DIR"
    echo
}

# 获取最新代码
fetch_updates() {
    print_step 2 "获取最新代码"
    
    # 检查git状态
    if ! git status >/dev/null 2>&1; then
        print_error "当前目录不是git仓库"
        exit 1
    fi
    
    # 暂存本地更改
    print_info "暂存本地更改..."
    git add . || true
    git stash push -m "Auto-stash before update $(date)" || true
    
    # 获取最新代码
    print_info "拉取最新代码..."
    git fetch origin
    git pull origin main
    
    print_info "代码更新完成"
    echo
}

# 恢复用户配置
restore_user_configs() {
    print_step 3 "恢复用户配置"
    
    if [ ! -f ".update_backup_info" ]; then
        print_warning "未找到备份信息，跳过配置恢复"
        return
    fi
    
    source .update_backup_info
    
    if [ ! -d "$BACKUP_DIR" ]; then
        print_warning "备份目录不存在，跳过配置恢复"
        return
    fi
    
    # 恢复用户配置
    USER_CONFIGS=(
        ".env"
        "love-site/"
        "nginx/ssl/"
        "data/"
        "backup/"
        "logs/"
    )
    
    for config in "${USER_CONFIGS[@]}"; do
        if [ -e "$BACKUP_DIR/$config" ]; then
            print_info "恢复: $config"
            cp -r "$BACKUP_DIR/$config" . 2>/dev/null || true
        fi
    done
    
    print_info "用户配置恢复完成"
    echo
}

# 更新脚本权限
update_permissions() {
    print_step 4 "更新脚本权限"
    
    # 设置脚本权限
    chmod +x cmd.sh
    chmod +x scripts/*.sh
    chmod +x test-deployment.sh 2>/dev/null || true
    
    print_info "脚本权限更新完成"
    echo
}

# 检查服务状态
check_services() {
    print_step 5 "检查服务状态"
    
    # 检查Docker服务
    if systemctl is-active --quiet docker; then
        print_info "Docker服务运行正常"
    else
        print_warning "Docker服务未运行"
    fi
    
    # 检查new-api服务
    if systemctl is-active --quiet new-api 2>/dev/null; then
        print_info "New-API服务运行正常"
    else
        print_warning "New-API服务未运行或未配置"
    fi
    
    # 检查nginx服务
    if systemctl is-active --quiet nginx; then
        print_info "Nginx服务运行正常"
    else
        print_warning "Nginx服务未运行"
    fi
    
    echo
}

# 完成更新
finish_update() {
    print_step 6 "完成更新"
    
    # 清理备份信息文件
    rm -f .update_backup_info
    
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                        🎉 更新完成！                         ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${CYAN}📋 更新摘要:${NC}"
    echo -e "   ✅ 代码已更新到最新版本"
    echo -e "   ✅ 用户配置已安全保护"
    echo -e "   ✅ 脚本权限已更新"
    echo -e "   📁 备份位置: ${YELLOW}$BACKUP_DIR${NC}"
    echo
    echo -e "${CYAN}🔧 后续操作:${NC}"
    echo -e "   1. ${GREEN}检查服务${NC}: ./scripts/manage-services.sh status"
    echo -e "   2. ${GREEN}重启服务${NC}: ./scripts/manage-services.sh restart"
    echo -e "   3. ${GREEN}查看日志${NC}: docker-compose logs -f"
    echo -e "   4. ${GREEN}测试部署${NC}: ./test-deployment.sh"
    echo
    echo -e "${YELLOW}💡 提示:${NC}"
    echo -e "   • 如果遇到问题，可以从 ${YELLOW}$BACKUP_DIR${NC} 恢复配置"
    echo -e "   • 建议重启服务以应用所有更新"
    echo -e "   • 备份文件可以在确认无问题后删除"
    echo
    echo -e "${GREEN}✅ 更新成功完成！${NC}"
}

# 显示帮助信息
show_help() {
    echo "New-API 安全更新脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --dry-run      模拟运行（不执行实际更新）"
    echo "  --force        强制更新（跳过确认）"
    echo
    echo "示例:"
    echo "  $0              # 正常更新"
    echo "  $0 --dry-run    # 模拟更新"
    echo "  $0 --force      # 强制更新"
    echo
}

# 主函数
main() {
    # 解析参数
    DRY_RUN=false
    FORCE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_banner
    
    # 检查目录
    check_directory
    
    # 确认更新
    if [ "$FORCE" = false ] && [ "$DRY_RUN" = false ]; then
        echo -e "${YELLOW}⚠️  即将更新New-API项目到最新版本${NC}"
        echo -e "${YELLOW}   这将会拉取最新代码，但会保护您的配置文件${NC}"
        echo
        read -p "确认继续更新？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "更新已取消"
            exit 0
        fi
        echo
    fi
    
    if [ "$DRY_RUN" = true ]; then
        print_info "模拟运行模式 - 不会执行实际更新"
        echo
    fi
    
    # 执行更新步骤
    if [ "$DRY_RUN" = false ]; then
        backup_user_configs
        fetch_updates
        restore_user_configs
        update_permissions
        check_services
        finish_update
    else
        print_info "模拟: 备份用户配置"
        print_info "模拟: 获取最新代码"
        print_info "模拟: 恢复用户配置"
        print_info "模拟: 更新脚本权限"
        print_info "模拟: 检查服务状态"
        print_info "模拟运行完成"
    fi
}

# 运行主函数
main "$@"
