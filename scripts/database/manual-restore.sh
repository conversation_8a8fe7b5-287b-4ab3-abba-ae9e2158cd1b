#!/bin/bash

# Manual Database Restore Script
# Use this script if automatic restore during deployment failed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                   手动数据库恢复工具                         ║"
    echo "║                                                              ║"
    echo "║  🔄 恢复New-API数据库备份                                    ║"
    echo "║  📦 支持自动选择最新备份                                    ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    print_error "请在new-api项目根目录运行此脚本"
    exit 1
fi

print_banner

# Check if backup directory exists
if [ ! -d "backup" ]; then
    print_error "备份目录不存在"
    exit 1
fi

# List available backups
print_status "可用的备份文件："
ls -la backup/*.sql 2>/dev/null || {
    print_error "未找到备份文件"
    exit 1
}

echo

# Get latest backup
LATEST_BACKUP=$(ls -t backup/*.sql 2>/dev/null | head -1)
if [ -z "$LATEST_BACKUP" ]; then
    print_error "未找到有效的备份文件"
    exit 1
fi

print_status "最新备份文件: $LATEST_BACKUP"

# Read database password
DB_PASSWORD=""
if [ -f ".env" ]; then
    DB_PASSWORD=$(grep "MYSQL_ROOT_PASSWORD=" .env | cut -d'=' -f2)
fi
if [ -z "$DB_PASSWORD" ]; then
    print_warning "未找到数据库密码，使用默认密码"
    DB_PASSWORD="123456"
fi

# Check if MySQL container is running
if ! docker ps | grep -q "mysql"; then
    print_error "MySQL容器未运行，请先启动服务："
    echo "docker-compose up -d"
    exit 1
fi

# Wait for MySQL to be ready
print_status "等待MySQL就绪..."
for i in {1..30}; do
    if docker exec mysql mysqladmin ping -u root -p"$DB_PASSWORD" --silent 2>/dev/null; then
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "MySQL在30秒内未就绪"
        exit 1
    fi
    sleep 1
done

print_status "MySQL已就绪"

# Confirm restore
print_warning "这将替换当前数据库中的所有数据！"
echo
read -p "确定要继续吗？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "恢复已取消"
    exit 0
fi

# Restore database
print_status "开始恢复数据库..."
if docker exec -i mysql mysql -u root -p"$DB_PASSWORD" new-api < "$LATEST_BACKUP" 2>/dev/null; then
    print_status "数据库恢复成功！"
    
    # Restart new-api service
    print_status "重启New-API服务..."
    docker-compose restart new-api
    
    # Wait for service to restart
    sleep 10
    
    print_status "恢复完成！"
    echo
    print_status "现在可以访问New-API，数据应该已经恢复"
else
    print_error "数据库恢复失败！"
    print_error "可能的原因："
    echo "  - 数据库密码不正确"
    echo "  - 备份文件损坏"
    echo "  - MySQL容器问题"
    exit 1
fi
