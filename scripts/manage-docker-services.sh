#!/bin/bash

# New-API Docker Service Management Script
# 用于管理 New-API Docker 容器服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目路径
NEW_API_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# 日志函数
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "New-API Docker Service Management Script"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start    - Start New-API Docker containers"
    echo "  stop     - Stop New-API Docker containers"
    echo "  restart  - Restart New-API Docker containers"
    echo "  status   - Show status of all containers"
    echo "  logs     - Show logs for all containers"
    echo "  pull     - Pull latest Docker images"
    echo "  help     - Show this help message"
}

# 检查Docker是否运行
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker未运行，请先启动Docker服务"
        exit 1
    fi
}

# 启动服务
start_services() {
    print_info "启动New-API Docker容器..."
    cd "$NEW_API_DIR"
    
    if docker compose up -d; then
        print_success "New-API容器启动成功！"
        sleep 3
        show_status
    else
        print_error "New-API容器启动失败"
        exit 1
    fi
}

# 停止服务
stop_services() {
    print_info "停止New-API Docker容器..."
    cd "$NEW_API_DIR"
    
    if docker compose down; then
        print_success "New-API容器停止成功！"
    else
        print_error "New-API容器停止失败"
        exit 1
    fi
}

# 重启服务
restart_services() {
    print_info "重启New-API Docker容器..."
    stop_services
    sleep 2
    start_services
}

# 显示状态
show_status() {
    print_info "New-API容器状态："
    echo ""
    cd "$NEW_API_DIR"
    
    echo "=== Docker Compose 状态 ==="
    docker compose ps
    echo ""
    
    echo "=== 容器健康状态 ==="
    docker ps --filter "name=new-api" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo ""
    
    # 检查API是否响应
    print_info "检查API响应..."
    if curl -s http://localhost:3000/api/status > /dev/null; then
        print_success "New-API API响应正常 (http://localhost:3000)"
    else
        print_warning "New-API API无响应"
    fi
}

# 显示日志
show_logs() {
    print_info "显示New-API容器日志..."
    cd "$NEW_API_DIR"
    
    case "${1:-all}" in
        new-api)
            docker compose logs -f new-api
            ;;
        mysql)
            docker compose logs -f mysql
            ;;
        redis)
            docker compose logs -f redis
            ;;
        all|*)
            docker compose logs -f
            ;;
    esac
}

# 拉取最新镜像
pull_images() {
    print_info "拉取最新Docker镜像..."
    cd "$NEW_API_DIR"
    
    if docker compose pull; then
        print_success "镜像拉取完成！"
        print_info "使用 '$0 restart' 重启服务以应用新镜像"
    else
        print_error "镜像拉取失败"
        exit 1
    fi
}

# 主函数
main() {
    check_docker
    
    case "${1:-}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs "$2"
            ;;
        pull)
            pull_images
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            echo "New-API Docker Service Manager"
            echo ""
            show_status
            echo ""
            echo "使用 '$0 help' 查看所有可用命令"
            ;;
    esac
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
