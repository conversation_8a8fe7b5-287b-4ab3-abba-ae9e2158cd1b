#!/bin/bash

# New-API Core Update Script
# 从上游项目更新 New-API 核心代码
# 上游项目: https://github.com/QuantumNous/new-api.git

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                New-API 核心代码更新工具                      ║"
    echo "║                                                              ║"
    echo "║  🔄 从上游项目更新核心代码                                   ║"
    echo "║  🛡️ 保护本地配置和自定义内容                                ║"
    echo "║  📦 智能合并更新                                             ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[步骤 $1]${NC} $2"
}

# 上游项目配置
UPSTREAM_REPO="https://github.com/QuantumNous/new-api.git"
UPSTREAM_BRANCH="main"
UPSTREAM_TAG=""  # 将自动获取最新标签
TEMP_DIR="/tmp/new-api-upstream-$$"

# 需要保护的本地文件和目录
PROTECTED_FILES=(
    ".env"
    ".env.template"
    "docker-compose.yml"
    "cmd.sh"
    "new-api-manager.sh"
    "test-deployment.sh"
    "scripts/"
    "nginx/"
    "love-site/"
    "backup/"
    "README.md"
    "guide.md"
    "脚本整合说明.md"
    "更新指南.md"
    "自动备份说明.md"
    ".gitignore"
    "new-api.service"
)

# 需要更新的核心文件（从上游）
CORE_FILES=(
    "web/"
    "common/"
    "controller/"
    "middleware/"
    "model/"
    "relay/"
    "router/"
    "service/"
    "main.go"
    "go.mod"
    "go.sum"
    "Dockerfile"
    "VERSION"
)

# 检查环境
check_environment() {
    print_step 1 "检查环境"
    
    if [ ! -f "docker-compose.yml" ]; then
        print_error "请在new-api项目根目录运行此脚本"
        exit 1
    fi
    
    if ! git status >/dev/null 2>&1; then
        print_error "当前目录不是git仓库"
        exit 1
    fi
    
    if ! command -v git >/dev/null 2>&1; then
        print_error "Git未安装"
        exit 1
    fi
    
    print_success "环境检查通过"
    echo
}

# 备份当前状态
backup_current_state() {
    print_step 2 "备份当前状态"
    
    # 创建数据库备份
    if [ -f "scripts/backup-database.sh" ]; then
        print_info "创建数据库备份..."
        ./scripts/backup-database.sh >/dev/null 2>&1 || print_warning "数据库备份失败"
    fi
    
    # 提交当前更改
    print_info "保存当前工作状态..."
    git add . 2>/dev/null || true
    git commit -m "Auto-commit before core update $(date)" 2>/dev/null || true
    
    print_success "当前状态已备份"
    echo
}

# 获取最新版本标签
get_latest_version() {
    print_step 3 "获取最新版本信息"

    print_info "检查上游最新版本..."

    # 获取最新的版本标签
    LATEST_TAG=$(git ls-remote --tags --sort="v:refname" "$UPSTREAM_REPO" | grep -E 'refs/tags/v[0-9]+\.[0-9]+\.[0-9]+(\.[0-9]+)?$' | tail -1 | sed 's/.*refs\/tags\///')

    if [ -z "$LATEST_TAG" ]; then
        print_error "无法获取最新版本标签"
        exit 1
    fi

    UPSTREAM_TAG="$LATEST_TAG"
    print_success "发现最新版本: $UPSTREAM_TAG"
    echo
}

# 获取上游代码
fetch_upstream() {
    print_step 4 "获取上游最新代码"

    print_info "克隆上游项目到临时目录..."
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
    fi

    git clone --depth=1 --branch="$UPSTREAM_TAG" "$UPSTREAM_REPO" "$TEMP_DIR" >/dev/null 2>&1
    
    if [ ! -d "$TEMP_DIR" ]; then
        print_error "无法克隆上游项目"
        exit 1
    fi
    
    print_success "上游代码获取成功"
    echo
}

# 更新核心文件
update_core_files() {
    print_step 5 "更新核心文件"
    
    cd "$TEMP_DIR"
    UPSTREAM_VERSION=$(cat VERSION 2>/dev/null || echo "unknown")
    cd - >/dev/null
    
    print_info "上游版本: $UPSTREAM_VERSION"
    print_info "开始更新核心文件..."
    
    for file in "${CORE_FILES[@]}"; do
        if [ -e "$TEMP_DIR/$file" ]; then
            print_info "更新: $file"
            
            # 如果是目录，先删除本地版本
            if [ -d "$file" ]; then
                rm -rf "$file"
            fi
            
            # 复制新版本
            cp -r "$TEMP_DIR/$file" "$file"
        else
            print_warning "上游项目中未找到: $file"
        fi
    done

    # 确保VERSION文件包含正确的版本号
    if [ -n "$UPSTREAM_TAG" ]; then
        echo "$UPSTREAM_TAG" > VERSION
        print_info "更新VERSION文件: $UPSTREAM_TAG"
    fi

    print_success "核心文件更新完成"
    echo
}

# 检查冲突和兼容性
check_compatibility() {
    print_step 6 "检查兼容性"
    
    # 检查Go模块
    if [ -f "go.mod" ]; then
        print_info "检查Go模块依赖..."
        if command -v go >/dev/null 2>&1; then
            go mod tidy >/dev/null 2>&1 || print_warning "Go模块整理失败"
        fi
    fi
    
    # 检查Docker配置
    if [ -f "Dockerfile" ]; then
        print_info "验证Dockerfile语法..."
        docker build --dry-run . >/dev/null 2>&1 || print_warning "Dockerfile可能有问题"
    fi
    
    print_success "兼容性检查完成"
    echo
}

# 重建和测试
rebuild_and_test() {
    print_step 7 "重建和测试"
    
    print_info "重新构建Docker镜像..."
    if docker-compose build --no-cache new-api >/dev/null 2>&1; then
        print_success "Docker镜像构建成功"
    else
        print_warning "Docker镜像构建失败，可能需要手动检查"
    fi
    
    # 如果服务正在运行，重启它们
    if docker-compose ps | grep -q "Up"; then
        print_info "重启服务以应用更新..."
        docker-compose restart new-api
        print_success "服务重启完成"
    fi
    
    echo
}

# 清理临时文件
cleanup() {
    print_step 8 "清理临时文件"
    
    if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
        print_success "临时文件清理完成"
    fi
    
    echo
}

# 显示更新摘要
show_summary() {
    print_step 9 "更新摘要"
    
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                        🎉 更新完成！                         ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${CYAN}📋 更新内容:${NC}"
    echo -e "   ✅ 核心代码已更新到最新版本"
    echo -e "   ✅ 本地配置和自定义内容已保护"
    echo -e "   ✅ Docker镜像已重新构建"
    echo -e "   ✅ 服务已重启（如果之前在运行）"
    echo
    echo -e "${CYAN}🔧 后续操作:${NC}"
    echo -e "   1. ${GREEN}测试功能${NC}: 访问Web界面确认功能正常"
    echo -e "   2. ${GREEN}检查日志${NC}: docker-compose logs -f new-api"
    echo -e "   3. ${GREEN}提交更改${NC}: git add . && git commit -m 'Update core to latest'"
    echo
    echo -e "${YELLOW}💡 提示:${NC}"
    echo -e "   • 如果遇到问题，可以使用 git reset --hard HEAD~1 回滚"
    echo -e "   • 数据库备份已自动创建，位于 backup/ 目录"
    echo -e "   • 建议测试所有功能确保兼容性"
    echo
}

# 显示帮助
show_help() {
    echo "New-API 核心代码更新工具"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --dry-run      模拟运行（不执行实际更新）"
    echo "  --force        强制更新（跳过确认）"
    echo
    echo "功能:"
    echo "  • 从上游项目 $UPSTREAM_REPO 获取最新代码"
    echo "  • 只更新核心文件，保护本地配置"
    echo "  • 自动备份数据库和当前状态"
    echo "  • 重新构建Docker镜像"
    echo "  • 智能重启服务"
    echo
}

# 主函数
main() {
    # 解析参数
    DRY_RUN=false
    FORCE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --dry-run)
                DRY_RUN=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_banner
    
    # 确认更新
    if [ "$FORCE" = false ] && [ "$DRY_RUN" = false ]; then
        echo -e "${YELLOW}⚠️  即将从上游项目更新New-API核心代码${NC}"
        echo -e "${YELLOW}   上游项目: $UPSTREAM_REPO${NC}"
        echo -e "${YELLOW}   这将更新Go代码、Web界面等核心文件${NC}"
        echo -e "${YELLOW}   本地配置文件将被保护${NC}"
        echo
        read -p "确认继续更新？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "更新已取消"
            exit 0
        fi
        echo
    fi
    
    if [ "$DRY_RUN" = true ]; then
        print_info "模拟运行模式 - 不会执行实际更新"
        echo
        print_info "将要执行的操作:"
        print_info "1. 检查环境"
        print_info "2. 备份当前状态"
        print_info "3. 获取最新版本信息"
        print_info "4. 获取上游代码"
        print_info "5. 更新核心文件"
        print_info "6. 检查兼容性"
        print_info "7. 重建和测试"
        print_info "8. 清理临时文件"
        print_info "模拟运行完成"
        exit 0
    fi
    
    # 执行更新
    check_environment
    backup_current_state
    get_latest_version
    fetch_upstream
    update_core_files
    check_compatibility
    rebuild_and_test
    cleanup
    show_summary
}

# 错误处理
trap 'print_error "更新过程中发生错误，正在清理..."; cleanup; exit 1' ERR

# 运行主函数
main "$@"
