#!/bin/bash

# Auto Backup Manager for New-API
# 自动备份管理器 - 管理数据库备份的自动化流程

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    自动备份管理器                            ║"
    echo "║                                                              ║"
    echo "║  🔄 自动备份和恢复数据库                                     ║"
    echo "║  📦 Git推送前自动备份                                        ║"
    echo "║  🎯 只保留最新3个备份                                        ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# Show help
show_help() {
    echo "自动备份管理器 - 使用说明"
    echo
    echo "用法: $0 [命令]"
    echo
    echo "命令:"
    echo "  backup          创建数据库备份"
    echo "  restore         恢复最新备份"
    echo "  list            列出所有备份"
    echo "  cleanup         清理多余备份（保留3个）"
    echo "  status          查看备份状态"
    echo "  setup           设置自动备份"
    echo "  test            测试备份恢复流程"
    echo "  help            显示此帮助"
    echo
    echo "自动化特性:"
    echo "  • Git推送前自动创建备份"
    echo "  • 部署时自动恢复最新备份"
    echo "  • 只保留最新3个备份文件"
    echo "  • 支持服务器间数据迁移"
    echo
}

# Create backup
create_backup() {
    print_info "创建数据库备份..."
    if [ -f "scripts/backup-database.sh" ]; then
        ./scripts/backup-database.sh
    else
        print_error "备份脚本不存在"
        exit 1
    fi
}

# Restore latest backup
restore_backup() {
    print_info "恢复最新数据库备份..."
    if [ -f "scripts/restore-database.sh" ]; then
        ./scripts/restore-database.sh
    else
        print_error "恢复脚本不存在"
        exit 1
    fi
}

# List backups
list_backups() {
    print_info "数据库备份列表:"
    if [ -d "backup" ]; then
        echo
        ls -lah backup/*.sql 2>/dev/null | while read line; do
            echo "  $line"
        done
        echo
        if [ -L "backup/latest_backup.sql" ]; then
            echo "最新备份链接:"
            ls -la backup/latest_backup.sql
        fi
    else
        print_warning "备份目录不存在"
    fi
}

# Cleanup old backups
cleanup_backups() {
    print_info "清理多余的备份文件..."
    if [ -d "backup" ]; then
        cd backup
        BACKUP_FILES=($(ls -t database_backup_*.sql 2>/dev/null))
        BACKUP_COUNT=${#BACKUP_FILES[@]}
        
        if [ $BACKUP_COUNT -gt 3 ]; then
            print_info "发现 $BACKUP_COUNT 个备份，删除多余的 $((BACKUP_COUNT - 3)) 个..."
            for ((i=3; i<BACKUP_COUNT; i++)); do
                print_info "删除: ${BACKUP_FILES[$i]}"
                rm -f "${BACKUP_FILES[$i]}"
            done
            print_success "清理完成，保留最新3个备份"
        else
            print_info "当前有 $BACKUP_COUNT 个备份，无需清理"
        fi
        cd ..
    else
        print_warning "备份目录不存在"
    fi
}

# Show backup status
show_status() {
    print_info "备份系统状态:"
    echo
    
    # Check backup directory
    if [ -d "backup" ]; then
        BACKUP_COUNT=$(ls backup/database_backup_*.sql 2>/dev/null | wc -l)
        print_success "备份目录存在，包含 $BACKUP_COUNT 个备份文件"
    else
        print_warning "备份目录不存在"
    fi
    
    # Check git hook
    if [ -f ".git/hooks/pre-push" ] && [ -x ".git/hooks/pre-push" ]; then
        print_success "Git pre-push 钩子已安装"
    else
        print_warning "Git pre-push 钩子未安装"
    fi
    
    # Check backup script
    if [ -f "scripts/backup-database.sh" ] && [ -x "scripts/backup-database.sh" ]; then
        print_success "备份脚本可用"
    else
        print_warning "备份脚本不可用"
    fi
    
    # Check restore script
    if [ -f "scripts/restore-database.sh" ] && [ -x "scripts/restore-database.sh" ]; then
        print_success "恢复脚本可用"
    else
        print_warning "恢复脚本不可用"
    fi
    
    # Check Docker
    if docker info >/dev/null 2>&1; then
        if docker ps | grep -q "mysql"; then
            print_success "MySQL容器运行中"
        else
            print_warning "MySQL容器未运行"
        fi
    else
        print_warning "Docker未运行"
    fi
}

# Setup automatic backup
setup_auto_backup() {
    print_info "设置自动备份系统..."
    
    # Create backup directory
    mkdir -p backup
    touch backup/.gitkeep
    
    # Set script permissions
    chmod +x scripts/backup-database.sh 2>/dev/null || true
    chmod +x scripts/restore-database.sh 2>/dev/null || true
    
    # Set git hook permissions
    chmod +x .git/hooks/pre-push 2>/dev/null || true
    
    print_success "自动备份系统设置完成"
}

# Test backup and restore
test_backup_restore() {
    print_info "测试备份恢复流程..."
    
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker未运行，无法测试"
        exit 1
    fi
    
    if ! docker ps | grep -q "mysql"; then
        print_error "MySQL容器未运行，无法测试"
        exit 1
    fi
    
    print_info "1. 创建测试备份..."
    create_backup
    
    print_info "2. 列出备份文件..."
    list_backups
    
    print_info "3. 清理多余备份..."
    cleanup_backups
    
    print_success "备份恢复流程测试完成"
}

# Main function
main() {
    if [ $# -eq 0 ]; then
        print_banner
        show_help
        exit 0
    fi
    
    case "$1" in
        backup)
            create_backup
            ;;
        restore)
            restore_backup
            ;;
        list)
            list_backups
            ;;
        cleanup)
            cleanup_backups
            ;;
        status)
            show_status
            ;;
        setup)
            setup_auto_backup
            ;;
        test)
            test_backup_restore
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
