#!/bin/bash

# New-API Autostart Setup Script
# 配置 New-API 开机自启动脚本

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Print functions
print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查是否以root权限运行
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用sudo运行此脚本"
        exit 1
    fi
}

# 获取项目路径
get_project_path() {
    PROJECT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
    print_info "项目路径: $PROJECT_PATH"
}

# 创建systemd服务文件
create_systemd_service() {
    print_info "创建systemd服务文件..."
    
    SERVICE_FILE="/etc/systemd/system/new-api.service"
    
    cat > "$SERVICE_FILE" << EOF
[Unit]
Description=New-API Service
Documentation=https://github.com/Calcium-Ion/new-api
After=network.target docker.service
Requires=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$PROJECT_PATH
ExecStart=/usr/bin/docker-compose up -d
ExecStop=/usr/bin/docker-compose down
TimeoutStartSec=0
User=root

[Install]
WantedBy=multi-user.target
EOF

    print_success "systemd服务文件已创建: $SERVICE_FILE"
}

# 启用并启动服务
enable_service() {
    print_info "重新加载systemd配置..."
    systemctl daemon-reload
    
    print_info "启用New-API服务..."
    systemctl enable new-api.service
    
    print_success "New-API服务已设置为开机自启"
}

# 测试服务
test_service() {
    print_info "测试服务配置..."
    
    if systemctl is-enabled new-api.service >/dev/null 2>&1; then
        print_success "服务已正确启用"
    else
        print_error "服务启用失败"
        return 1
    fi
    
    print_info "服务状态:"
    systemctl status new-api.service --no-pager || true
}

# 显示使用说明
show_usage() {
    echo "New-API 开机自启配置完成！"
    echo
    echo "管理命令:"
    echo "  sudo systemctl start new-api     # 启动服务"
    echo "  sudo systemctl stop new-api      # 停止服务"
    echo "  sudo systemctl restart new-api   # 重启服务"
    echo "  sudo systemctl status new-api    # 查看状态"
    echo "  sudo systemctl disable new-api   # 禁用自启"
    echo
    echo "日志查看:"
    echo "  sudo journalctl -u new-api -f    # 查看服务日志"
    echo "  docker-compose logs -f           # 查看应用日志"
}

# 主函数
main() {
    print_info "开始配置New-API开机自启..."
    
    check_root
    get_project_path
    
    # 检查Docker和docker-compose
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose >/dev/null 2>&1; then
        print_error "docker-compose未安装，请先安装docker-compose"
        exit 1
    fi
    
    # 检查项目文件
    if [ ! -f "$PROJECT_PATH/docker-compose.yml" ]; then
        print_error "未找到docker-compose.yml文件"
        exit 1
    fi
    
    create_systemd_service
    enable_service
    test_service
    
    echo
    show_usage
    
    print_success "New-API开机自启配置完成！"
}

# 运行主函数
main "$@"
