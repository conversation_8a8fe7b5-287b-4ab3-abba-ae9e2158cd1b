#!/bin/bash

# New-API Official Image Update Script
# 官方镜像更新脚本 - 专门用于更新官方Docker镜像

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                New-API 官方镜像更新工具                      ║"
    echo "║                                                              ║"
    echo "║  🐳 官方Docker镜像更新                                       ║"
    echo "║  🛡️ 保护本地配置和数据                                      ║"
    echo "║  🚀 快速安全更新                                             ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[步骤 $1]${NC} $2"
}

# 检查环境
check_environment() {
    print_step 1 "检查环境"
    
    if [ ! -f "docker-compose.yml" ]; then
        print_error "请在new-api项目根目录运行此脚本"
        exit 1
    fi
    
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker未安装"
        exit 1
    fi
    
    if ! command -v curl >/dev/null 2>&1; then
        print_error "curl未安装，无法检查最新版本"
        exit 1
    fi
    
    print_success "环境检查通过"
    echo
}

# 获取最新版本
get_latest_version() {
    print_step 2 "获取最新版本信息"
    
    print_info "检查官方最新版本..."
    LATEST_VERSION=$(curl -s "https://api.github.com/repos/QuantumNous/new-api/releases/latest" | grep '"tag_name"' | cut -d'"' -f4 2>/dev/null || echo "")
    
    if [ -z "$LATEST_VERSION" ]; then
        print_error "无法获取最新版本信息"
        exit 1
    fi
    
    # 获取当前版本
    CURRENT_VERSION=$(curl -s http://localhost:3000/api/status 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4 || echo "unknown")
    
    print_success "最新版本: $LATEST_VERSION"
    print_info "当前版本: $CURRENT_VERSION"
    
    if [ "$CURRENT_VERSION" = "$LATEST_VERSION" ]; then
        print_success "✅ 已是最新版本，无需更新"
        exit 0
    fi
    
    echo
}

# 备份数据库
backup_database() {
    print_step 3 "备份数据库"
    
    if [ -f "scripts/backup-database.sh" ]; then
        print_info "创建数据库备份..."
        ./scripts/backup-database.sh >/dev/null 2>&1 || print_warning "数据库备份失败"
        print_success "数据库备份完成"
    else
        print_warning "备份脚本不存在，跳过数据库备份"
    fi
    
    echo
}

# 更新Docker镜像
update_docker_image() {
    print_step 4 "更新Docker镜像"
    
    print_info "更新docker-compose.yml中的镜像版本..."
    
    # 更新docker-compose.yml中的镜像标签
    if grep -q "calciumion/new-api:" docker-compose.yml; then
        sed -i "s|calciumion/new-api:.*|calciumion/new-api:$LATEST_VERSION|g" docker-compose.yml
        print_success "镜像版本已更新到: $LATEST_VERSION"
    else
        print_error "未找到官方镜像配置"
        exit 1
    fi
    
    print_info "拉取最新镜像..."
    if docker-compose pull new-api; then
        print_success "镜像拉取完成"
    else
        print_error "镜像拉取失败"
        exit 1
    fi
    
    echo
}

# 重启服务
restart_service() {
    print_step 5 "重启服务"
    
    print_info "重启New-API服务..."
    if docker-compose up -d new-api; then
        print_success "服务重启完成"
    else
        print_error "服务重启失败"
        exit 1
    fi
    
    # 等待服务启动
    print_info "等待服务启动..."
    sleep 15
    
    # 验证服务状态
    if curl -s http://localhost:3000/api/status >/dev/null 2>&1; then
        NEW_VERSION=$(curl -s http://localhost:3000/api/status 2>/dev/null | grep -o '"version":"[^"]*"' | cut -d'"' -f4 || echo "unknown")
        print_success "服务启动成功，当前版本: $NEW_VERSION"
    else
        print_warning "服务可能还在启动中"
    fi
    
    echo
}

# 显示更新摘要
show_summary() {
    print_step 6 "更新摘要"
    
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                        🎉 更新完成！                         ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    echo "📋 更新内容:"
    echo "   ✅ 官方镜像已更新到最新版本: $LATEST_VERSION"
    echo "   ✅ 本地配置和数据已保护"
    echo "   ✅ 服务已重启并运行正常"
    echo
    
    echo "🔧 后续操作:"
    echo "   1. 测试功能: 访问Web界面确认功能正常"
    echo "   2. 检查日志: docker-compose logs -f new-api"
    echo "   3. 提交更改: git add . && git commit -m 'Update to $LATEST_VERSION'"
    echo
    
    echo "💡 提示:"
    echo "   • 如果遇到问题，可以回滚到之前的版本"
    echo "   • 数据库备份已自动创建，位于 backup/ 目录"
    echo "   • 建议测试所有功能确保兼容性"
    echo
}

# 显示帮助信息
show_help() {
    echo "New-API 官方镜像更新工具"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --check        仅检查版本，不执行更新"
    echo "  --force        强制更新（跳过确认）"
    echo
    echo "功能:"
    echo "  • 自动检查官方最新版本"
    echo "  • 更新Docker镜像到最新版本"
    echo "  • 自动备份数据库"
    echo "  • 重启服务并验证"
    echo
}

# 主函数
main() {
    # 解析参数
    CHECK_ONLY=false
    FORCE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --check)
                CHECK_ONLY=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            *)
                print_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_banner
    
    # 执行检查
    check_environment
    get_latest_version
    
    if [ "$CHECK_ONLY" = true ]; then
        print_info "仅检查模式，退出"
        exit 0
    fi
    
    # 确认更新
    if [ "$FORCE" = false ]; then
        echo -e "${YELLOW}⚠️  即将更新New-API到最新版本${NC}"
        echo -e "${YELLOW}   当前版本: $CURRENT_VERSION${NC}"
        echo -e "${YELLOW}   最新版本: $LATEST_VERSION${NC}"
        echo
        read -p "确认继续更新？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "更新已取消"
            exit 0
        fi
        echo
    fi
    
    # 执行更新
    backup_database
    update_docker_image
    restart_service
    show_summary
}

# 运行主函数
main "$@"
