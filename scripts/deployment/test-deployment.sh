#!/bin/bash

# Test script for deployment validation
# This script checks if all components are ready for deployment

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_test() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_pass() {
    echo -e "${GREEN}[PASS]${NC} $1"
}

print_fail() {
    echo -e "${RED}[FAIL]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

echo "🧪 New-API Deployment Test Suite"
echo "================================="
echo

# Test 1: Check required files
print_test "Checking required files..."
required_files=(
    "cmd.sh"
    "docker-compose.yml"
    ".env.template"
    "new-api.service"
    "scripts/manage-services.sh"
    "scripts/setup-autostart.sh"
    "nginx/system-nginx.conf"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_pass "Found: $file"
    else
        print_fail "Missing: $file"
        exit 1
    fi
done

# Test 2: Check script permissions
print_test "Checking script permissions..."
if [ -x "cmd.sh" ]; then
    print_pass "cmd.sh is executable"
else
    print_fail "cmd.sh is not executable"
fi

if [ -x "scripts/manage-services.sh" ]; then
    print_pass "manage-services.sh is executable"
else
    print_fail "manage-services.sh is not executable"
fi

# Test 3: Check script syntax
print_test "Checking script syntax..."
if bash -n cmd.sh; then
    print_pass "cmd.sh syntax is valid"
else
    print_fail "cmd.sh has syntax errors"
    exit 1
fi

if bash -n scripts/manage-services.sh; then
    print_pass "manage-services.sh syntax is valid"
else
    print_fail "manage-services.sh has syntax errors"
    exit 1
fi

# Test 4: Check environment template
print_test "Checking environment template..."
if grep -q "MYSQL_ROOT_PASSWORD=" .env.template; then
    print_pass "Environment template has database password"
else
    print_fail "Environment template missing database password"
fi

if grep -q "SESSION_SECRET=" .env.template; then
    print_pass "Environment template has session secret"
else
    print_fail "Environment template missing session secret"
fi

# Test 5: Check systemd service file
print_test "Checking systemd service file..."
if grep -q "WorkingDirectory=/root/workspace/new-api" new-api.service; then
    print_pass "Service file has correct working directory"
else
    print_fail "Service file has incorrect working directory"
fi

if grep -q "docker-compose" new-api.service; then
    print_pass "Service file references docker-compose"
else
    print_fail "Service file missing docker-compose reference"
fi

# Test 6: Check nginx configuration
print_test "Checking nginx configuration..."
if grep -q "liangliangdamowang.edu.deal" nginx/system-nginx.conf; then
    print_pass "Nginx config has correct domain"
else
    print_fail "Nginx config missing domain"
fi

if grep -q "proxy_pass.*3000" nginx/system-nginx.conf; then
    print_pass "Nginx config proxies to port 3000"
else
    print_fail "Nginx config missing proxy configuration"
fi

# Test 7: Check love site directory
print_test "Checking love site..."
if [ -d "love-site" ]; then
    print_pass "Love site directory exists"
    if [ -f "love-site/index.html" ]; then
        print_pass "Love site has index.html"
    else
        print_warn "Love site missing index.html"
    fi
else
    print_warn "Love site directory not found"
fi

echo
echo "🎉 All tests completed!"
echo
echo "📋 Deployment readiness summary:"
echo "- ✅ All required files present"
echo "- ✅ Scripts have correct permissions"
echo "- ✅ Script syntax is valid"
echo "- ✅ Configuration files are properly formatted"
echo
echo "🚀 Ready for deployment! Run: sudo ./cmd.sh"
