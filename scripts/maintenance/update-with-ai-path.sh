#!/bin/bash

# New API 更新脚本 - 保持 /ai 路径功能
# 使用方法: ./update-with-ai-path.sh

set -e

echo "🚀 开始更新 New API 并保持 /ai 路径功能..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 创建备份目录
BACKUP_DIR="backup/$(TZ='Asia/Shanghai' date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo -e "${YELLOW}📦 备份当前修改的文件...${NC}"

# 备份修改的文件
cp web/vite.config.js "$BACKUP_DIR/" 2>/dev/null || echo "web/vite.config.js 不存在，跳过备份"
cp web/src/index.js "$BACKUP_DIR/" 2>/dev/null || echo "web/src/index.js 不存在，跳过备份"
cp router/main.go "$BACKUP_DIR/" 2>/dev/null || echo "router/main.go 不存在，跳过备份"
cp router/web-router.go "$BACKUP_DIR/" 2>/dev/null || echo "router/web-router.go 不存在，跳过备份"
cp nginx-newapi/default.conf "$BACKUP_DIR/" 2>/dev/null || echo "nginx-newapi/default.conf 不存在，跳过备份"
cp nginx-newapi/system-nginx.conf "$BACKUP_DIR/" 2>/dev/null || echo "nginx-newapi/system-nginx.conf 不存在，跳过备份"
cp nginx-newapi/system-nginx-http.conf "$BACKUP_DIR/" 2>/dev/null || echo "nginx-newapi/system-nginx-http.conf 不存在，跳过备份"
cp docker-compose.yml "$BACKUP_DIR/" 2>/dev/null || echo "docker-compose.yml 不存在，跳过备份"

echo -e "${GREEN}✅ 备份完成，保存在: $BACKUP_DIR${NC}"

# 停止服务
echo -e "${YELLOW}🛑 停止当前服务...${NC}"
docker-compose down

# 拉取最新更新
echo -e "${YELLOW}📥 拉取最新更新...${NC}"
git stash push -m "Stash AI path modifications before update"
git pull origin main

echo -e "${YELLOW}🔧 重新应用 /ai 路径修改...${NC}"

# 1. 修改 web/vite.config.js
echo "修改 vite.config.js..."
sed -i 's/export default defineConfig({/export default defineConfig({\n  base: ".\/",/' web/vite.config.js

# 2. 修改 web/src/index.js
echo "修改 index.js..."
sed -i 's/<BrowserRouter/<BrowserRouter\n          basename="\/ai"/' web/src/index.js

# 3. 修改 router/main.go
echo "修改 main.go..."
if ! grep -q "SetAIWebRouter" router/main.go; then
    sed -i '/SetVideoRouter(router)/a\\t// 设置 /ai 路径的前端路由\n\tSetAIWebRouter(router, buildFS, indexPage)' router/main.go
fi

# 4. 修改 router/web-router.go
echo "修改 web-router.go..."
if ! grep -q "SetAIWebRouter" router/web-router.go; then
    cat >> router/web-router.go << 'EOF'

func SetAIWebRouter(router *gin.Engine, buildFS embed.FS, indexPage []byte) {
	aiGroup := router.Group("/ai")
	aiGroup.Use(gzip.Gzip(gzip.DefaultCompression))
	aiGroup.Use(middleware.GlobalWebRateLimit())
	aiGroup.Use(middleware.Cache())
	aiGroup.Use(static.Serve("/ai", common.EmbedFolder(buildFS, "web/dist")))
	
	// 处理 /ai 根路径
	aiGroup.GET("", func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache")
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexPage)
	})
	
	// 处理 /ai/ 路径
	aiGroup.GET("/", func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache")
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexPage)
	})
	
	// 处理 /ai 路径下的所有其他路由（除了 API 路径），返回前端应用
	aiGroup.NoRoute(func(c *gin.Context) {
		// 如果是 API 路径，不处理，让其他路由处理
		if strings.HasPrefix(c.Request.RequestURI, "/ai/api") || strings.HasPrefix(c.Request.RequestURI, "/ai/v1") {
			c.Next()
			return
		}
		c.Header("Cache-Control", "no-cache")
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexPage)
	})
}
EOF
fi

# 修改 SetWebRouter 函数排除 /ai 路径
sed -i 's/strings.HasPrefix(c.Request.RequestURI, "\/assets")/strings.HasPrefix(c.Request.RequestURI, "\/assets") || strings.HasPrefix(c.Request.RequestURI, "\/ai")/' router/web-router.go

# 5. 修改 docker-compose.yml
echo "修改 docker-compose.yml..."
if ! grep -q "/var/www/ai:ro" docker-compose.yml; then
    sed -i '/- \.\/static\/love:\/var\/www\/love:ro/a\      # Mount AI frontend files\n      - ./web/dist:/var/www/ai:ro' docker-compose.yml
fi

echo -e "${YELLOW}🏗️ 重新构建前端...${NC}"
cd web
npm install --legacy-peer-deps
DISABLE_ESLINT_PLUGIN='true' npm run build
cd ..

echo -e "${YELLOW}🚀 启动服务...${NC}"
docker-compose up --build -d

echo -e "${GREEN}✅ 更新完成！${NC}"
echo -e "${GREEN}🌐 现在可以通过以下地址访问：${NC}"
echo -e "   • 原始路径: https://liangliangdamowang.edu.deal/"
echo -e "   • AI路径:   https://liangliangdamowang.edu.deal/ai/"
echo ""
echo -e "${YELLOW}📋 备份文件位置: $BACKUP_DIR${NC}"
echo -e "${YELLOW}💡 如果遇到问题，可以从备份文件中恢复${NC}"
echo -e "${YELLOW}📖 详细信息请参考: guide-newapi.md${NC}"
