#!/bin/bash

# New-API Quick Update Script
# 快速更新脚本 - 提供多种更新选项

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

print_error() {
    echo -e "${RED}[错误]${NC} $1"
}

print_banner() {
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    New-API 快速更新工具                      ║"
    echo "║                                                              ║"
    echo "║  🔄 多种更新选项                                             ║"
    echo "║  🛡️ 智能更新策略                                            ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}\n"
}

show_menu() {
    echo -e "${CYAN}请选择更新类型:${NC}"
    echo
    echo "1) 🔄 更新New-API核心代码（从上游项目）"
    echo "   从 https://github.com/QuantumNous/new-api.git 获取最新版本"
    echo "   保护本地配置，只更新核心功能代码"
    echo
    echo "2) 📦 更新项目配置和脚本（从自己的仓库）"
    echo "   从您的GitHub仓库获取最新的配置和管理脚本"
    echo "   包括部署脚本、管理工具等"
    echo
    echo "3) 🔍 检查可用更新"
    echo "   检查两个来源的更新情况"
    echo
    echo "0) 退出"
    echo
}

# 更新New-API核心代码
update_core() {
    print_info "启动New-API核心代码更新..."
    if [ -f "scripts/update-new-api-core.sh" ]; then
        ./scripts/update-new-api-core.sh
    else
        print_error "核心更新脚本不存在"
        exit 1
    fi
}

# 更新项目配置
update_config() {
    print_info "更新项目配置和脚本..."

    # 检查是否在正确目录
    if [ ! -f "docker-compose.yml" ]; then
        print_error "请在new-api项目根目录运行此脚本"
        exit 1
    fi

    # 检查Git状态
    if ! git status >/dev/null 2>&1; then
        print_error "当前目录不是git仓库"
        exit 1
    fi

    print_info "检查配置更新..."
    git fetch origin

    # 检查是否有更新
    LOCAL=$(git rev-parse HEAD)
    REMOTE=$(git rev-parse origin/master)

    if [ "$LOCAL" = "$REMOTE" ]; then
        print_success "✅ 配置已是最新版本"
        return 0
    fi

    print_info "📥 发现配置更新，开始更新..."

    # 暂存本地更改
    print_info "暂存本地更改..."
    git add . 2>/dev/null || true
    git stash push -m "Config update stash $(date)" 2>/dev/null || true

    # 拉取更新
    print_info "拉取最新配置..."
    git pull origin main

    # 更新权限
    print_info "更新脚本权限..."
    chmod +x cmd.sh scripts/*.sh test-deployment.sh 2>/dev/null || true

    # 检查是否需要重启服务
    CHANGED_FILES=$(git diff --name-only HEAD~1 HEAD)
    NEED_RESTART=false

    if echo "$CHANGED_FILES" | grep -q "docker-compose.yml\|scripts/\|nginx/"; then
        NEED_RESTART=true
    fi

    print_success "✅ 配置更新完成！"
    echo

    if [ "$NEED_RESTART" = true ]; then
        print_warning "⚠️  检测到重要文件更新，建议重启服务："
        echo "   ./scripts/manage-services.sh restart"
    else
        print_info "ℹ️  本次更新无需重启服务"
    fi

    echo
    print_info "📋 更新的文件："
    echo "$CHANGED_FILES" | sed 's/^/   /'
}

# 检查可用更新
check_updates() {
    print_info "检查可用更新..."
    echo

    # 检查配置更新
    print_info "🔍 检查项目配置更新..."
    if git fetch origin >/dev/null 2>&1; then
        LOCAL=$(git rev-parse HEAD)
        if REMOTE=$(git rev-parse origin/master 2>/dev/null); then
            if [ "$LOCAL" = "$REMOTE" ]; then
                print_success "✅ 项目配置已是最新版本"
            else
                print_warning "📦 发现项目配置更新可用"
                echo "   运行选项2更新项目配置"
            fi
        else
            print_info "ℹ️  无法检查配置更新（这是正常的，如果您使用自定义配置）"
        fi
    else
        print_warning "⚠️  无法连接到远程仓库检查配置更新"
    fi

    echo

    # 检查上游更新（简单检查）
    print_info "🔍 检查New-API核心更新..."
    UPSTREAM_REPO="https://github.com/QuantumNous/new-api.git"

    if command -v curl >/dev/null 2>&1; then
        # 获取最新版本标签
        LATEST_VERSION=$(curl -s "https://api.github.com/repos/QuantumNous/new-api/releases/latest" | grep '"tag_name"' | cut -d'"' -f4 2>/dev/null || echo "unknown")
        CURRENT_VERSION=$(cat VERSION 2>/dev/null || echo "unknown")

        if [ "$LATEST_VERSION" != "unknown" ]; then
            print_info "🔄 上游最新版本: $LATEST_VERSION"
            print_info "📦 当前版本: $CURRENT_VERSION"

            if [ "$CURRENT_VERSION" != "$LATEST_VERSION" ]; then
                print_warning "🆕 发现新版本可用！运行选项1更新核心代码"
            else
                print_success "✅ 已是最新版本"
            fi
        else
            print_warning "⚠️  无法检查上游更新状态"
        fi
    else
        print_warning "⚠️  需要curl命令检查上游更新"
    fi
}

# 主函数
main() {
    # 检查基本环境
    if [ ! -f "docker-compose.yml" ]; then
        print_error "请在new-api项目根目录运行此脚本"
        exit 1
    fi

    print_banner

    if [ $# -eq 0 ]; then
        # 交互模式
        while true; do
            show_menu
            read -p "请选择 (0-3): " choice
            echo

            case $choice in
                1)
                    update_core
                    break
                    ;;
                2)
                    update_config
                    break
                    ;;
                3)
                    check_updates
                    echo
                    read -p "按Enter继续..."
                    ;;
                0)
                    print_info "退出更新工具"
                    exit 0
                    ;;
                *)
                    print_error "无效选择，请重试"
                    echo
                    ;;
            esac
        done
    else
        # 命令行模式
        case "$1" in
            core)
                update_core
                ;;
            config)
                update_config
                ;;
            check)
                check_updates
                ;;
            help|--help|-h)
                echo "New-API 快速更新工具"
                echo
                echo "用法: $0 [命令]"
                echo
                echo "命令:"
                echo "  core     更新New-API核心代码"
                echo "  config   更新项目配置和脚本"
                echo "  check    检查可用更新"
                echo "  help     显示此帮助"
                echo
                echo "不带参数运行进入交互模式"
                ;;
            *)
                print_error "未知命令: $1"
                echo "运行 $0 help 查看帮助"
                exit 1
                ;;
        esac
    fi
}

# 运行主函数
main "$@"
