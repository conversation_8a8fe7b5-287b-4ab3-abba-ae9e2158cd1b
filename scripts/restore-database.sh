#!/bin/bash

# New-API Database Restore Script
# This script restores a MySQL database backup

set -e

# Configuration
BACKUP_DIR="./backup"
CONTAINER_NAME="mysql"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [backup_file]"
    echo
    echo "If no backup file is specified, the latest backup will be used."
    echo
    echo "Available backups:"
    if [ -d "$BACKUP_DIR" ]; then
        ls -la "$BACKUP_DIR"/*.sql 2>/dev/null || echo "No backup files found"
    else
        echo "Backup directory not found"
    fi
}

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if MySQL container exists and is running
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    print_error "MySQL container '$CONTAINER_NAME' is not running."
    print_warning "Please start the services first: docker-compose up -d"
    exit 1
fi

# Determine backup file to use
if [ $# -eq 0 ]; then
    # Use latest backup
    if [ -f "$BACKUP_DIR/latest_backup.sql" ]; then
        BACKUP_FILE="$BACKUP_DIR/latest_backup.sql"
        print_status "Using latest backup: $BACKUP_FILE"
    else
        print_error "No backup file specified and no latest backup found."
        show_usage
        exit 1
    fi
elif [ $# -eq 1 ]; then
    # Use specified backup file
    if [ -f "$1" ]; then
        BACKUP_FILE="$1"
    elif [ -f "$BACKUP_DIR/$1" ]; then
        BACKUP_FILE="$BACKUP_DIR/$1"
    else
        print_error "Backup file not found: $1"
        show_usage
        exit 1
    fi
else
    print_error "Too many arguments."
    show_usage
    exit 1
fi

print_status "Backup file: $BACKUP_FILE"

# Confirm restore operation
print_warning "This will REPLACE all existing data in the database!"
print_warning "Make sure you have a backup of current data if needed."
echo
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Restore cancelled."
    exit 0
fi

print_status "Starting database restore..."

# Read database password from .env file
DB_PASSWORD=""
if [ -f ".env" ]; then
    DB_PASSWORD=$(grep "MYSQL_ROOT_PASSWORD=" .env | cut -d'=' -f2)
fi
if [ -z "$DB_PASSWORD" ]; then
    DB_PASSWORD="123456"  # Default password
fi

print_status "Using database password from .env file"

# Wait for MySQL to be ready
print_status "Waiting for MySQL to be ready..."
for i in {1..30}; do
    if docker exec "$CONTAINER_NAME" mysqladmin ping -u root -p"$DB_PASSWORD" --silent; then
        break
    fi
    if [ $i -eq 30 ]; then
        print_error "MySQL is not responding after 30 seconds"
        exit 1
    fi
    sleep 1
done

print_status "MySQL is ready. Starting restore..."

# Restore database
if docker exec -i "$CONTAINER_NAME" mysql -u root -p"$DB_PASSWORD" new-api < "$BACKUP_FILE"; then
    print_status "Database restore completed successfully!"
    print_status "Data restored from: $BACKUP_FILE"
else
    print_error "Database restore failed!"
    exit 1
fi

print_status "Restore process completed!"
echo
print_status "You may need to restart the application to ensure all changes take effect:"
print_status "docker-compose restart new-api"
