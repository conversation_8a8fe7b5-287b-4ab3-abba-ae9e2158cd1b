#!/bin/bash

# SSL Certificate Renewal Script for New-API
# This script renews Let's Encrypt certificates and updates the project SSL directory

set -e

# Configuration
PROJECT_DIR="/root/workspace/new-api"
SSL_DIR="$PROJECT_DIR/ssl"
DOMAIN="liangliangdamowang.edu.deal"
CERT_PATH="/etc/letsencrypt/live/$DOMAIN"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Starting SSL certificate renewal process..."

# Check if certificate needs renewal
if certbot certificates | grep -q "VALID:"; then
    print_status "Checking certificate expiry..."
    
    # Try to renew certificate
    print_status "Attempting to renew certificate..."
    if certbot renew --quiet --no-self-upgrade; then
        print_status "Certificate renewal check completed"
        
        # Check if certificate was actually renewed
        if [ -f "$CERT_PATH/fullchain.pem" ] && [ -f "$CERT_PATH/privkey.pem" ]; then
            # Update project SSL files
            print_status "Updating project SSL files..."
            cp "$CERT_PATH/fullchain.pem" "$SSL_DIR/certificate.crt"
            cp "$CERT_PATH/privkey.pem" "$SSL_DIR/private.key"
            
            # Set correct permissions
            chmod 644 "$SSL_DIR/certificate.crt"
            chmod 600 "$SSL_DIR/private.key"
            
            print_status "SSL files updated successfully"
            
            # Restart nginx container if running
            if docker ps | grep -q "nginx-proxy"; then
                print_status "Restarting nginx container..."
                docker restart nginx-proxy
                print_status "Nginx container restarted"
            fi
            
            print_status "SSL certificate renewal completed successfully!"
        else
            print_error "Certificate files not found after renewal"
            exit 1
        fi
    else
        print_error "Certificate renewal failed"
        exit 1
    fi
else
    print_error "No valid certificates found"
    exit 1
fi

print_status "SSL renewal process finished"
