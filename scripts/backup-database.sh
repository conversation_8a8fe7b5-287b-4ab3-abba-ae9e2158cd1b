#!/bin/bash

# New-API Database Backup Script
# This script creates a complete backup of the MySQL database

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_DIR/backup"
TIMESTAMP=$(TZ='Asia/Shanghai' date +"%Y%m%d_%H%M%S")
BACKUP_FILE="database_backup_${TIMESTAMP}.sql"
CONTAINER_NAME="mysql"

# 确保在项目根目录执行
cd "$PROJECT_DIR"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker first."
    exit 1
fi

# Check if MySQL container exists and is running
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    print_error "MySQL container '$CONTAINER_NAME' is not running."
    print_warning "Please start the services first: docker-compose up -d"
    exit 1
fi

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Read database password from .env file
DB_PASSWORD=""
if [ -f ".env" ]; then
    DB_PASSWORD=$(grep "MYSQL_ROOT_PASSWORD=" .env | cut -d'=' -f2)
fi
if [ -z "$DB_PASSWORD" ]; then
    DB_PASSWORD="123456"  # Default password
fi

print_status "Starting database backup..."
print_status "Working directory: $(pwd)"
print_status "Backup directory: $BACKUP_DIR"
print_status "Backup file: $BACKUP_DIR/$BACKUP_FILE"

# Create database backup
if docker exec "$CONTAINER_NAME" mysqldump -u root -p"$DB_PASSWORD" --single-transaction --routines --triggers new-api > "$BACKUP_DIR/$BACKUP_FILE"; then
    print_status "Database backup completed successfully!"
    print_status "Backup saved to: $BACKUP_DIR/$BACKUP_FILE"
    
    # Get file size
    BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
    print_status "Backup size: $BACKUP_SIZE"
    
    # Create a symlink to latest backup
    ln -sf "$BACKUP_FILE" "$BACKUP_DIR/latest_backup.sql"
    print_status "Latest backup symlink created: $BACKUP_DIR/latest_backup.sql"
    
else
    print_error "Database backup failed!"
    exit 1
fi

# Keep only last 3 backups (for git repository)
print_status "清理旧备份文件（只保留最新3个）..."
cd "$BACKUP_DIR"

# 获取所有备份文件，按时间排序（最新的在前）
BACKUP_FILES=($(ls -t database_backup_*.sql 2>/dev/null))
BACKUP_COUNT=${#BACKUP_FILES[@]}

if [ $BACKUP_COUNT -gt 3 ]; then
    print_status "发现 $BACKUP_COUNT 个备份文件，删除多余的 $((BACKUP_COUNT - 3)) 个..."

    # 删除第4个及以后的备份文件
    for ((i=3; i<BACKUP_COUNT; i++)); do
        print_status "删除旧备份: ${BACKUP_FILES[$i]}"
        rm -f "${BACKUP_FILES[$i]}"
    done

    print_status "备份清理完成，保留最新3个备份文件"
else
    print_status "当前有 $BACKUP_COUNT 个备份文件，无需清理"
fi

cd - >/dev/null

print_status "Backup process completed!"
echo
echo "To restore this backup on a new server:"
echo "1. Start the MySQL container: docker-compose up -d mysql"
echo "2. Wait for MySQL to be ready"
echo "3. Run: docker exec -i mysql mysql -u root -p123456 new-api < $BACKUP_DIR/$BACKUP_FILE"
