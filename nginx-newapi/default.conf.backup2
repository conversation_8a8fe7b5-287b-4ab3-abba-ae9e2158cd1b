# 文件路径: ~/new-api/nginx/default.conf

# HTTP server - redirect to HTTPS
server {
    listen 80;
    server_name liangliangdamowang.edu.deal api.liangliangdamowang.edu.deal;

    # Redirect all HTTP requests to HTTPS
    return 301 https://$server_name$request_uri;
}

# HTTPS server
server {
    listen 443 ssl;
    http2 on;
    server_name liangliangdamowang.edu.deal api.liangliangdamowang.edu.deal;

    # SSL certificate configuration
    ssl_certificate /etc/nginx/ssl/certificate.crt;
    ssl_certificate_key /etc/nginx/ssl/private.key;

    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 情侣网站静态文件服务
    location /love {
        alias /var/www/love;
        index index.html;
        try_files $uri $uri/ /love/index.html;

        # 设置静态文件缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # 处理前端静态资源路径问题 - 将绝对路径重定向到子路径
    location ~ ^/(assets|logo\.png|favicon\.ico)(.*)$ {
        rewrite ^/(assets|logo\.png|favicon\.ico)(.*)$ /new-api/$1$2 redirect;
    }

    # New-API 子路径代理配置
    location /new-api/ {
        # 代理到 new-api 容器，注意末尾的斜杠用于路径重写
        proxy_pass http://new-api:3000/;

        # 基本代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;

        # 子路径支持的关键头
        proxy_set_header X-Forwarded-Prefix /new-api;
        proxy_set_header X-Script-Name /new-api;

        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲设置
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # 默认根路径 - 可以重定向到 new-api 或显示欢迎页面
    location / {
        # 重定向根路径到 new-api 子路径
        return 301 https://$server_name/new-api/;
    }
}

