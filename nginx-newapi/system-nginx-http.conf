# 临时HTTP配置文件 - 用于SSL证书申请前的初始配置
# 文件路径: ~/new-api/nginx/system-nginx-http.conf

server {
    listen 80;
    server_name liangliangdamowang.edu.deal;

    # 情侣网站静态文件服务
    location /love {
        alias /var/www/love;
        index index.html;
        try_files $uri $uri/ /love/index.html;
        
        # 设置静态文件缓存
        location ~* \.(css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # JavaScript文件不缓存（开发期间）
        location ~* \.js$ {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
        }
    }

    # AI API路径 - 代理到new-api服务（移除/ai前缀）
    location /ai/api {
        rewrite ^/ai/api/(.*) /api/$1 break;
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings
        proxy_buffering off;
        proxy_cache off;
    }

    # AI v1 API路径 - 代理到new-api服务（移除/ai前缀）
    location /ai/v1 {
        rewrite ^/ai/v1/(.*) /v1/$1 break;
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings
        proxy_buffering off;
        proxy_cache off;
    }

    # 保持原有API路径的兼容性（可选）
    location /api {
        proxy_pass http://127.0.0.1:3000/api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings
        proxy_buffering off;
        proxy_cache off;
    }

    # 保持原有v1 API路径的兼容性（可选）
    location /v1 {
        proxy_pass http://127.0.0.1:3000/v1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_Set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings
        proxy_buffering off;
        proxy_cache off;
    }

    # AI应用路径 - 直接提供静态文件和代理API
    location /ai/ {
        # 首先尝试提供静态文件
        try_files $uri $uri/ @ai_backend;

        # 设置静态文件的根目录
        root /var/www;
        index index.html;

        # 设置静态文件缓存
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # AI应用根路径重定向
    location = /ai {
        return 301 /ai/;
    }

    # AI后端代理（当静态文件不存在时）
    location @ai_backend {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings
        proxy_buffering off;
        proxy_cache off;
    }

    # Main site root - New API service (direct access)
    location / {
        # Forward to local new-api service
        proxy_pass http://127.0.0.1:3000;

        # Preserve original host and protocol information
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
    }

    # New API service at /new-api path (for backward compatibility)
    location /new-api/ {
        # Remove /new-api prefix before forwarding to backend
        rewrite ^/new-api/(.*) /$1 break;

        # Forward to local new-api service
        proxy_pass http://127.0.0.1:3000;

        # Preserve original host and protocol information
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Add custom header to indicate the original path prefix
        proxy_set_header X-Original-Path-Prefix /new-api;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 300s;

        # Buffer settings for streaming responses
        proxy_buffering off;
        proxy_cache off;
    }

    # Handle /new-api without trailing slash
    location = /new-api {
        return 301 $scheme://$host/new-api/;
    }
}
