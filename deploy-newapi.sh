#!/bin/bash

# New-API 一键部署脚本
# 自动化部署 new-api 项目，支持多路径访问：
# - 原始路径: https://domain.com/
# - AI路径: https://domain.com/ai/
#
# 使用方法: ./deploy-newapi.sh [domain]
# 示例: ./deploy-newapi.sh liangliangdamowang.edu.deal

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
DOMAIN="${1:-liangliangdamowang.edu.deal}"
SSL_EMAIL="admin@${DOMAIN}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 错误处理
error_exit() {
    log_error "$1"
    exit 1
}

# 检查依赖
check_dependencies() {
    log_step "检查系统依赖..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        error_exit "Docker 未安装，请先安装 Docker"
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose 未安装，请先安装 Docker Compose"
    fi
    
    # 检查 certbot
    if ! command -v certbot &> /dev/null; then
        log_warn "certbot 未安装，将跳过 SSL 证书自动申请"
    fi
    
    log_info "依赖检查完成"
}

# 环境准备
prepare_environment() {
    log_step "准备部署环境..."

    cd "$PROJECT_DIR"

    # 创建必要目录
    mkdir -p ssl logs/{nginx,mysql,redis} static/love data backup

    # 检查必要文件
    if [ ! -f "docker-compose.yml" ]; then
        error_exit "docker-compose.yml 文件不存在，请确保在正确的项目目录中运行"
    fi

    if [ ! -f "guide-newapi.md" ]; then
        error_exit "guide-newapi.md 文件不存在，请确保在正确的项目目录中运行"
    fi

    # 检查 .env 文件
    if [ ! -f ".env" ]; then
        log_warn ".env 文件不存在，将使用默认配置"
        # 创建基本的 .env 文件
        cat > .env << EOF
MYSQL_ROOT_PASSWORD=123456
MYSQL_DATABASE=new-api
REDIS_CONN_STRING=redis://redis
SESSION_SECRET=$(openssl rand -hex 32)
DOMAIN_NAME=$DOMAIN
SSL_EMAIL=$SSL_EMAIL
EOF
        log_info "已创建基本 .env 文件"
    fi

    # 检查前端构建文件
    if [ ! -d "web/dist" ]; then
        log_warn "前端构建文件不存在，需要先构建前端"
        if [ -d "web" ] && [ -f "web/package.json" ]; then
            log_info "开始构建前端..."
            cd web
            if command -v npm &> /dev/null; then
                npm install --legacy-peer-deps
                DISABLE_ESLINT_PLUGIN='true' npm run build
            else
                error_exit "npm 未安装，无法构建前端"
            fi
            cd ..
            log_info "前端构建完成"
        else
            error_exit "前端源码不存在，无法构建"
        fi
    fi

    # 检查备份文件
    if [ ! -f "backup/latest_backup.sql" ]; then
        log_warn "数据库备份文件不存在，将跳过数据恢复"
    fi

    log_info "环境准备完成"
}

# SSL 证书配置
setup_ssl() {
    log_step "配置 SSL 证书..."
    
    if ! command -v certbot &> /dev/null; then
        log_warn "certbot 未安装，跳过 SSL 证书申请"
        return 0
    fi
    
    # 检查证书是否已存在
    if certbot certificates 2>/dev/null | grep -q "$DOMAIN"; then
        log_info "SSL 证书已存在，复制到项目目录..."
        cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ssl/certificate.crt
        cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" ssl/private.key
        chmod 644 ssl/certificate.crt
        chmod 600 ssl/private.key
    else
        log_info "申请新的 SSL 证书..."
        # 停止可能占用 80 端口的服务
        systemctl stop nginx 2>/dev/null || true
        
        # 申请证书
        if certbot certonly --standalone --non-interactive --agree-tos --email "$SSL_EMAIL" -d "$DOMAIN"; then
            cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ssl/certificate.crt
            cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" ssl/private.key
            chmod 644 ssl/certificate.crt
            chmod 600 ssl/private.key
            log_info "SSL 证书申请成功"
        else
            log_error "SSL 证书申请失败"
            return 1
        fi
    fi
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 停止系统 nginx（如果运行）
    systemctl stop nginx 2>/dev/null || true
    
    # 启动数据库服务
    log_info "启动数据库服务..."
    docker-compose up -d mysql redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 恢复数据库（如果备份文件存在）
    if [ -f "backup/latest_backup.sql" ]; then
        log_info "恢复数据库..."
        if [ -f "scripts/database/restore_database.sh" ]; then
            echo "y" | ./scripts/database/restore_database.sh || log_warn "数据库恢复失败"
        elif [ -f "scripts/restore-database.sh" ]; then
            echo "y" | ./scripts/restore-database.sh || log_warn "数据库恢复失败"
        fi
    fi
    
    # 启动所有服务
    log_info "启动完整服务栈..."
    docker-compose up -d
    
    # 等待服务启动
    sleep 60
    
    log_info "服务启动完成"
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."

    # 检查容器状态
    log_info "检查容器状态..."
    if ! docker-compose ps | grep -q "healthy\|Up"; then
        log_error "部分容器未正常启动"
        docker-compose ps
        return 1
    fi

    # 等待服务完全启动
    log_info "等待服务完全启动..."
    sleep 30

    # 检查原始 API 状态
    log_info "检查原始 API 状态..."
    if curl -s -k "https://$DOMAIN/api/status" | grep -q '"success":true'; then
        log_info "原始 API 状态检查通过"
    else
        log_warn "原始 API 状态检查失败，可能需要更多时间启动"
    fi

    # 检查 AI API 状态
    log_info "检查 AI API 状态..."
    if curl -s -k "https://$DOMAIN/ai/api/status" | grep -q '"success":true'; then
        log_info "AI API 状态检查通过"
    else
        log_warn "AI API 状态检查失败，可能需要更多时间启动"
    fi

    # 检查原始前端页面
    log_info "检查原始前端页面..."
    if curl -s -k "https://$DOMAIN/" | grep -q "<title>"; then
        log_info "原始前端页面检查通过"
    else
        log_warn "原始前端页面检查失败"
    fi

    # 检查 AI 前端页面
    log_info "检查 AI 前端页面..."
    if curl -s -k "https://$DOMAIN/ai/" | grep -q "<title>"; then
        log_info "AI 前端页面检查通过"
    else
        log_warn "AI 前端页面检查失败"
    fi

    log_info "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    echo
    echo "=========================================="
    echo "🎉 New-API 部署完成！"
    echo "=========================================="
    echo
    echo "📱 前端访问地址："
    echo "  • 原始路径: https://$DOMAIN/"
    echo "  • AI路径:   https://$DOMAIN/ai/"
    echo
    echo "🔌 API 访问地址："
    echo "  • 原始API:  https://$DOMAIN/api/status"
    echo "  • AI API:   https://$DOMAIN/ai/api/status"
    echo "  • OpenAI兼容: https://$DOMAIN/v1/* 或 https://$DOMAIN/ai/v1/*"
    echo
    echo "🛠️ 管理工具："
    echo "  • 统一管理: ./new-api-manager.sh"
    echo "  • 查看状态: ./new-api-manager.sh status"
    echo "  • 备份数据: ./new-api-manager.sh backup"
    echo "  • AI路径更新: ./scripts/maintenance/update-with-ai-path.sh"
    echo
    echo "📋 常用命令："
    echo "  • 查看日志: docker-compose logs -f"
    echo "  • 重启服务: docker-compose restart"
    echo "  • 停止服务: docker-compose down"
    echo "  • 更新证书: ./scripts/renew-ssl.sh"
    echo
    echo "📖 文档参考："
    echo "  • 完整指南: guide-newapi.md"
    echo "  • 官方文档: README.md"
    echo
    echo "📂 项目目录: $PROJECT_DIR"
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "🚀 New-API 一键部署脚本"
    echo "🌐 域名: $DOMAIN"
    echo "📧 邮箱: $SSL_EMAIL"
    echo "=========================================="
    echo

    check_dependencies
    prepare_environment
    setup_ssl
    start_services
    verify_deployment
    show_deployment_info
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
