[Unit]
Description=New-API Service
Documentation=https://github.com/Calcium-Ion/new-api
After=docker.service network-online.target
Requires=docker.service
Wants=network-online.target

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/root/workspace/new-api
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
ExecReload=/usr/local/bin/docker-compose restart
TimeoutStartSec=300
TimeoutStopSec=120
User=root
Group=root

# Restart policy
Restart=on-failure
RestartSec=10

# Environment
Environment=COMPOSE_PROJECT_NAME=new-api

[Install]
WantedBy=multi-user.target
