package main

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	password := "123"
	
	// 生成密码哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		fmt.Printf("生成密码哈希失败: %v\n", err)
		return
	}
	
	fmt.Printf("密码 '%s' 的哈希值: %s\n", password, string(hashedPassword))
	
	// 验证哈希是否正确
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
	if err == nil {
		fmt.Println("✓ 哈希验证成功")
	} else {
		fmt.Printf("✗ 哈希验证失败: %v\n", err)
	}
}
