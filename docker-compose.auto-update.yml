services:
  new-api:
    image: calciumion/new-api:latest
    container_name: new-api
    restart: always
    command: --log-dir /app/logs
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data
      - ./logs:/app/logs
    environment:
      - BASE_URL=
      - SQL_DSN=root:123456@tcp(mysql:3306)/new-api  # Point to the mysql service
      - REDIS_CONN_STRING=redis://redis
      - TZ=Asia/Shanghai
      - ERROR_LOG_ENABLED=true # 是否启用错误日志记录
    #      - TIKTOKEN_CACHE_DIR=./tiktoken_cache  # 如果需要使用tiktoken_cache，请取消注释
    #      - SESSION_SECRET=random_string  # 多机部署时设置，必须修改这个随机字符串！！！！！！！
    #      - NODE_TYPE=slave  # Uncomment for slave node in multi-node deployment
    #      - SYNC_FREQUENCY=60  # Uncomment if regular database syncing is needed
    #      - FRONTEND_BASE_URL=https://openai.justsong.cn  # Uncomment for multi-node deployment with front-end URL

    depends_on:
      - redis
      - mysql
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'"]
      interval: 30s
      timeout: 10s
      retries: 3
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  redis:
    image: redis:latest
    container_name: redis
    restart: always
    labels:
      - "com.centurylinklabs.watchtower.enable=true"

  mysql:
    image: mysql:8.2
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456  # Ensure this matches the password in SQL_DSN
      MYSQL_DATABASE: new-api
    volumes:
      - mysql_data:/var/lib/mysql
    labels:
      - "com.centurylinklabs.watchtower.enable=false"  # 不自动更新数据库
    # ports:
    #   - "3306:3306"  # If you want to access MySQL from outside Docker, uncomment

  # Watchtower - 自动更新 Docker 镜像
  watchtower:
    image: containrrr/watchtower:latest
    container_name: watchtower
    restart: always
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      # 基本配置
      - WATCHTOWER_CLEANUP=true                    # 清理旧镜像
      - WATCHTOWER_POLL_INTERVAL=3600             # 检查间隔：1小时
      - WATCHTOWER_INCLUDE_STOPPED=true           # 包含已停止的容器
      - WATCHTOWER_REVIVE_STOPPED=false          # 不重启已停止的容器
      - WATCHTOWER_LABEL_ENABLE=true             # 只更新有标签的容器
      - WATCHTOWER_ROLLING_RESTART=true          # 滚动重启
      
      # 时间配置
      - TZ=Asia/Shanghai
      
      # 通知配置（可选 - 需要配置邮箱信息）
      # - WATCHTOWER_NOTIFICATIONS=email
      # - WATCHTOWER_NOTIFICATION_EMAIL_FROM=<EMAIL>
      # - WATCHTOWER_NOTIFICATION_EMAIL_TO=<EMAIL>
      # - WATCHTOWER_NOTIFICATION_EMAIL_SERVER=smtp.example.com
      # - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PORT=587
      # - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_USER=<EMAIL>
      # - WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PASSWORD=your-password
      
      # Webhook 通知（可选）
      # - WATCHTOWER_NOTIFICATIONS=slack
      # - WATCHTOWER_NOTIFICATION_SLACK_HOOK_URL=https://hooks.slack.com/services/xxx
      
      # 日志级别
      - WATCHTOWER_DEBUG=false
      - WATCHTOWER_TRACE=false
    
    # 只在指定时间运行（可选）
    # command: --schedule "0 2 * * *"  # 每天凌晨2点运行
    
    # 实时监控模式（默认）
    command: --interval 3600 --cleanup --label-enable

volumes:
  mysql_data:
