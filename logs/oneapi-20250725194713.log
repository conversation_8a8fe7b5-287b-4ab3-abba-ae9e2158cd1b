[SYS] 2025/07/25 - 19:47:13 | initializing token encoders 
[SYS] 2025/07/25 - 19:47:13 | token encoders initialized 
[SYS] 2025/07/25 - 19:47:13 | using MySQL as database 
[SYS] 2025/07/25 - 19:47:13 | database migration started 
[SYS] 2025/07/25 - 19:47:14 | database migrated 
[SYS] 2025/07/25 - 19:47:14 | system is already initialized at: 2025-06-29 19:19:27 +0800 CST 
[SYS] 2025/07/25 - 19:47:14 | Redis is enabled 
[SYS] 2025/07/25 - 19:47:14 | New API v0.8.8.0-alpha.5 started 
[SYS] 2025/07/25 - 19:47:14 | memory cache enabled 
[SYS] 2025/07/25 - 19:47:14 | sync frequency: 60 seconds 
[SYS] 2025/07/25 - 19:47:14 | channels synced from database 
[SYS] 2025/07/25 - 19:47:14 | batch update enabled with interval 5s 
[SYS] 2025/07/25 - 19:47:14 | 正在更新数据看板数据... 
[SYS] 2025/07/25 - 19:47:14 | 保存数据看板数据成功，共保存0条数据 
[SYS] 2025/07/25 - 19:47:14 | FRONTEND_BASE_URL is ignored on master node 
[GIN] 2025/07/25 - 19:47:18 | 202507251947188483851069PWEdXS6 | 200 |    1.727177ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:47:29 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:47:29 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:47:39 | 2025072519473932366872NWK8P26s | 200 |    1.748891ms |      ********** |     GET /api/status
[SYS] 2025/07/25 - 19:47:44 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:47:44 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:47:48 | 20250725194748903763145xuDivGFE | 200 |    2.574665ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:47:59 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:47:59 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:48:14 | syncing channels from database 
[SYS] 2025/07/25 - 19:48:14 | syncing options from database 
[SYS] 2025/07/25 - 19:48:14 | channels synced from database 
[SYS] 2025/07/25 - 19:48:14 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:48:14 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:48:18 | 20250725194818969119890RlO8Sj3M | 200 |    1.856434ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:48:29 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:48:29 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:48:44 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:48:44 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:48:49 | 2025072519484933847329jLO0Rydw | 200 |   70.614935ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:48:59 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:48:59 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:49:10 | 20250725194910592480866kYBymeRX | 200 |    2.991645ms |      ********** |     GET /
[SYS] 2025/07/25 - 19:49:14 | syncing options from database 
[SYS] 2025/07/25 - 19:49:14 | syncing channels from database 
[SYS] 2025/07/25 - 19:49:14 | channels synced from database 
[SYS] 2025/07/25 - 19:49:14 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:49:14 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:49:18 | 20250725194918159539963P6WMz2Kw | 200 |    1.799931ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:18 | 202507251949186809128721OlnE4zM | 200 |    1.692336ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:18 | 20250725194918936765543KTvucBhH | 200 |     832.132µs |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:19 | 20250725194919169122333azJI2ndY | 200 |     634.733µs |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:49:19 | 202507251949191866369839KBXZ6qI | 200 |    2.071217ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:24 | 20250725194924592179020VDDz1JLT | 200 |    1.929616ms |      ********** |     GET /api/notice
[GIN] 2025/07/25 - 19:49:28 | 20250725194928268699038LOZtVWtZ | 200 |    1.946947ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:28 | 20250725194928637747230oSEHJHeb | 200 |     891.777µs |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:28 | 20250725194928887734772oG4q3z6b | 200 |       795.1µs |      ********** |     GET /
[SYS] 2025/07/25 - 19:49:29 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:49:29 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:49:29 | 20250725194929137943465n4FyjrmO | 200 |     850.033µs |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:30 | 20250725194930330994669RZIaYasa | 200 |    2.448058ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:30 | 20250725194930608417575OPK2sYT2 | 200 |   26.701399ms |      ********** |     GET /assets/i18n-BSxjx2rs.js
[GIN] 2025/07/25 - 19:49:30 | 20250725194930606572064nW1nR0a6 | 200 |   40.036067ms |      ********** |     GET /assets/react-core-DtMAePju.js
[GIN] 2025/07/25 - 19:49:30 | 20250725194930607659651J6nuwzTO | 200 |   72.155177ms |      ********** |     GET /assets/semi-ui-yRXI6evF.css
[GIN] 2025/07/25 - 19:49:30 | 20250725194930608485677h799UcYa | 200 |   78.842602ms |      ********** |     GET /assets/react-components-XvIrFW7f.js
[GIN] 2025/07/25 - 19:49:30 | 20250725194930608196435W4if4j9q | 200 |   90.722445ms |      ********** |     GET /assets/tools-D64yHnf1.js
[GIN] 2025/07/25 - 19:49:30 | 2025072519493060828613594m0dL46 | 200 |  111.255015ms |      ********** |     GET /assets/index-C8L2jEvp.css
[GIN] 2025/07/25 - 19:49:30 | 20250725194930607972984Kx7kyLIs | 200 |  129.614199ms |      ********** |     GET /assets/semi-ui-BN13Wcpw.js
[GIN] 2025/07/25 - 19:49:30 | 202507251949306061359816H0l26lo | 200 |  135.066781ms |      ********** |     GET /assets/index-BbPB7pPU.js
[GIN] 2025/07/25 - 19:49:31 | 20250725194931177717948tG6Fn1ID | 200 |     520.591µs |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:49:31 | 202507251949312658539224G7RD67L | 200 |     827.901µs |      ********** |    HEAD /
[ERR] 2025/07/25 - 19:49:31 | 20250725194931319229058MNS3reAj | user 0 | 未提供令牌 
[GIN] 2025/07/25 - 19:49:31 | 20250725194931319229058MNS3reAj | 401 |     226.506µs |      ********** |     GET /v1/models
[GIN] 2025/07/25 - 19:49:31 | 20250725194931390642939ukJrtpuL | 200 |    1.495566ms |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:49:31 | 2025072519493146400434298wMCOhB | 200 |     667.518µs |      ********** |     GET /new-api/api/status
[GIN] 2025/07/25 - 19:49:32 | 20250725194932281682490yBOvV3Qr | 200 |     813.974µs |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:49:32 | 20250725194932281960437u1sf94JU | 200 |    1.573345ms |      ********** |     GET /logo.png
[GIN] 2025/07/25 - 19:49:32 | 20250725194932564105801B2K63wYK | 200 |    2.736159ms |      ********** |     GET /logo.png
[GIN] 2025/07/25 - 19:49:37 | 20250725194937797876953lAQ7bYl6 | 200 |   68.271789ms |      ********** |    POST /api/user/login?turnstile=
[GIN] 2025/07/25 - 19:49:38 | 20250725194938144165699TwWVq7Gn | 200 |    2.576657ms |      ********** |     GET /assets/index-DwRFB99L.js
[GIN] 2025/07/25 - 19:49:38 | 20250725194938147622847vHqEdp5W | 200 |   15.613917ms |      ********** |     GET /assets/IllustrationConstruction-h2fiJesH.js
[GIN] 2025/07/25 - 19:49:38 | 20250725194938147391896wiqC4S4T | 200 |   71.308183ms |      ********** |     GET /assets/visactor-C2aDheGE.js
[GIN] 2025/07/25 - 19:49:39 | 20250725194939452970464sIDvgl2C | 200 |    2.629444ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:49:39 | 20250725194939454042603iMJKS42j | 200 |    2.356194ms |      ********** |     GET /api/data/?username=&start_timestamp=1753357780&end_timestamp=1753447780&default_time=hour
[GIN] 2025/07/25 - 19:49:39 | 20250725194939712689572cz6prGs0 | 200 |      753.14µs |      ********** |     GET /api/uptime/status
[GIN] 2025/07/25 - 19:49:41 | 202507251949417690958854Whw7QdJ | 200 |    3.077492ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:49:41 | 20250725194941769469123G9belIoS | 200 |    2.801577ms |      ********** |     GET /api/data/?username=&start_timestamp=1753357782&end_timestamp=1753447782&default_time=hour
[GIN] 2025/07/25 - 19:49:42 | 2025072519494223200607Dy2h8toV | 200 |     575.046µs |      ********** |     GET /api/uptime/status
[SYS] 2025/07/25 - 19:49:44 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:49:44 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:49:45 | 20250725194945269372031Uo3UnDeN | 200 |     2.33199ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:49:45 | 20250725194945541182103gjz9Zs1U | 200 |    7.750263ms |      ********** |     GET /assets/tools-D64yHnf1.js
[GIN] 2025/07/25 - 19:49:45 | 20250725194945542007652WEdrELO1 | 200 |   47.397986ms |      ********** |     GET /assets/semi-ui-yRXI6evF.css
[GIN] 2025/07/25 - 19:49:45 | 20250725194945540900684I0sFyZXE | 200 |   39.349291ms |      ********** |     GET /assets/react-core-DtMAePju.js
[GIN] 2025/07/25 - 19:49:45 | 20250725194945542469010LcV8ODi6 | 200 |   62.241605ms |      ********** |     GET /assets/index-C8L2jEvp.css
[GIN] 2025/07/25 - 19:49:45 | 20250725194945543464472GPFTV3OT | 200 |   28.735962ms |      ********** |     GET /assets/react-components-XvIrFW7f.js
[GIN] 2025/07/25 - 19:49:45 | 20250725194945543184490brQqPEM3 | 200 |   66.800624ms |      ********** |     GET /assets/i18n-BSxjx2rs.js
[GIN] 2025/07/25 - 19:49:45 | 20250725194945541182413F5Pegs29 | 200 |  135.239284ms |      ********** |     GET /assets/semi-ui-BN13Wcpw.js
[GIN] 2025/07/25 - 19:49:45 | 20250725194945540191730mTmUJG3b | 200 |  144.237573ms |      ********** |     GET /assets/index-BbPB7pPU.js
[GIN] 2025/07/25 - 19:49:47 | 202507251949471029860073Pz2TQ5U | 200 |    1.752985ms |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:49:47 | 20250725194947102986010aOqanBQK | 200 |    2.638661ms |      ********** |     GET /logo.png
[GIN] 2025/07/25 - 19:49:47 | 20250725194947374003562JpROiOSw | 200 |    1.815019ms |      ********** |     GET /logo.png
[GIN] 2025/07/25 - 19:49:49 | 20250725194949225484383SMsd3oUg | 200 |    1.787968ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:49:52 | 20250725194952228837815jZfmdHko | 200 |    3.475952ms |      ********** |     GET /assets/index-BAeIAZO0.js
[GIN] 2025/07/25 - 19:49:52 | 20250725194952486977134chhgLsjh | 200 |     653.851µs |      ********** |     GET /api/notice
[GIN] 2025/07/25 - 19:49:52 | 20250725194952487043325RkGlJzQV | 200 |     796.175µs |      ********** |     GET /api/home_page_content
[SYS] 2025/07/25 - 19:49:59 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:49:59 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:50:14 | syncing options from database 
[SYS] 2025/07/25 - 19:50:14 | syncing channels from database 
[SYS] 2025/07/25 - 19:50:14 | channels synced from database 
[SYS] 2025/07/25 - 19:50:14 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:50:14 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:50:19 | 20250725195019286260534yciNR5mJ | 200 |   23.150206ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:50:29 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:50:29 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:50:44 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:50:44 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:50:49 | 20250725195049378921839YGaCW4TX | 200 |   47.279575ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:50:56 | 20250725195056891677965sZaOsMyM | 200 |    2.279845ms |      ********** |     GET /api/data/?username=&start_timestamp=1753357857&end_timestamp=1753447857&default_time=hour
[GIN] 2025/07/25 - 19:50:56 | 20250725195056890947938t4MfRdCP | 200 |    3.105878ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:50:57 | 20250725195057145988242nO0fTYZg | 200 |     651.613µs |      ********** |     GET /api/uptime/status
[SYS] 2025/07/25 - 19:50:59 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:50:59 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:51:05 | 20250725195105650109582hpxR7c0D | 200 |   14.678542ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:51:11 | 20250725195111601940433incTDwij | 200 |    2.080407ms |      ********** |     GET /new-api/api/status
[SYS] 2025/07/25 - 19:51:14 | syncing options from database 
[SYS] 2025/07/25 - 19:51:14 | syncing channels from database 
[SYS] 2025/07/25 - 19:51:14 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:51:14 | channels synced from database 
[SYS] 2025/07/25 - 19:51:14 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:51:19 | 20250725195119508873694ecpXsJMv | 200 |    1.690228ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:51:23 | 20250725195123980055837m1r28MgG | 200 |    1.670213ms |      ********** |     GET /
[SYS] 2025/07/25 - 19:51:29 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:51:29 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:51:44 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:51:44 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:51:49 | 20250725195149590490471OxmwURit | 200 |   18.559213ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:51:59 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:51:59 | 任务进度轮询完成 
[INFO] 2025/07/25 - 19:51:59 | 20250725195159190985239LF0IGRIX | user 1 with unlimited token has enough quota ＄19977.953018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:51:59 | 20250725195159191354370HQ6IXBPr | user 1 with unlimited token has enough quota ＄19977.953018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:51:59 | 20250725195159698882995oP31trnh | user 1 with unlimited token has enough quota ＄19977.953018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:00 | 20250725195200140486078qrunb5li | user 1 with unlimited token has enough quota ＄19977.953018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:00 | 20250725195200453957110JcQOGh6B | user 1 with unlimited token has enough quota ＄19977.953018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:00 | 20250725195200475310493sVgFjpo3 | user 1 with unlimited token has enough quota ＄19977.953018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:00 | 20250725195159191354370HQ6IXBPr | record consume log: userId=1, params={"channel_id":128,"prompt_tokens":1321,"completion_tokens":30,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988976509,"use_time_seconds":1,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["128"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"translate-model-fast","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:00 | 20250725195159191354370HQ6IXBPr | 200 |  1.353691737s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:00 | 20250725195200140486078qrunb5li | record consume log: userId=1, params={"channel_id":128,"prompt_tokens":1155,"completion_tokens":11,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988976509,"use_time_seconds":0,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["128"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"translate-model-fast","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:00 | 20250725195200140486078qrunb5li | 200 |  515.750988ms |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:00 | 20250725195200453957110JcQOGh6B | record consume log: userId=1, params={"channel_id":128,"prompt_tokens":1217,"completion_tokens":54,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988976509,"use_time_seconds":0,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["128"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"translate-model-fast","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:00 | 20250725195200453957110JcQOGh6B | 200 |  507.106962ms |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:01 | 20250725195159190985239LF0IGRIX | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1449,"completion_tokens":14,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988976509,"use_time_seconds":2,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:01 | 20250725195159190985239LF0IGRIX | 200 |  2.392347014s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:01 | 20250725195200475310493sVgFjpo3 | record error log: userId=1, channelId=124, modelName=gpt-4.1-mini, tokenName=Translate, content=您已达到请求数限制：1分钟内最多请求100次 (request id: 20250725195201506018800hAw55rLF) 
[GIN] 2025/07/25 - 19:52:01 | 20250725195200475310493sVgFjpo3 | 429 |  1.130642369s |      ********** |    POST /v1/chat/completions
[ERR] 2025/07/25 - 19:52:01 | 20250725195200475310493sVgFjpo3 | relay error (channel #124, status code: 429): 您已达到请求数限制：1分钟内最多请求100次 (request id: 20250725195201506018800hAw55rLF) (request id: 20250725195200475310493sVgFjpo3) 
[INFO] 2025/07/25 - 19:52:01 | 20250725195159698882995oP31trnh | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1483,"completion_tokens":31,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988976509,"use_time_seconds":2,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:01 | 20250725195159698882995oP31trnh | 200 |  2.286907067s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:02 | 20250725195202856403854DDF50Mhe | user 1 with unlimited token has enough quota ＄19977.903018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:03 | 20250725195203950498656ETKHuBN7 | user 1 with unlimited token has enough quota ＄19977.903018, trusted and no need to pre-consume 
[SYS] 2025/07/25 - 19:52:04 | batch update started 
[SYS] 2025/07/25 - 19:52:04 | batch update finished 
[INFO] 2025/07/25 - 19:52:04 | 20250725195202856403854DDF50Mhe | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1321,"completion_tokens":14,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988951509,"use_time_seconds":2,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:04 | 20250725195202856403854DDF50Mhe | 200 |   1.36617779s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:06 | 20250725195203950498656ETKHuBN7 | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1307,"completion_tokens":11,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988951509,"use_time_seconds":3,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:06 | 20250725195203950498656ETKHuBN7 | 200 |  2.279456215s |      ********** |    POST /v1/chat/completions
[SYS] 2025/07/25 - 19:52:09 | batch update started 
[SYS] 2025/07/25 - 19:52:09 | batch update finished 
[INFO] 2025/07/25 - 19:52:11 | 202507251952117049235281OA34U7A | user 1 with unlimited token has enough quota ＄19977.883018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:11 | 2025072519521196367931499MhMXzp | user 1 with unlimited token has enough quota ＄19977.883018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:12 | 20250725195211997999082iHoFZYNR | user 1 with unlimited token has enough quota ＄19977.883018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:12 | 2025072519521223512851o1stHoF0 | user 1 with unlimited token has enough quota ＄19977.883018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:12 | 20250725195212367883656CBrOx6n | user 1 with unlimited token has enough quota ＄19977.883018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:52:12 | 202507251952117049235281OA34U7A | record consume log: userId=1, params={"channel_id":128,"prompt_tokens":1167,"completion_tokens":4,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988941509,"use_time_seconds":1,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["128"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"translate-model-fast","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:12 | 202507251952117049235281OA34U7A | 200 |  518.896313ms |      ********** |    POST /v1/chat/completions
[SYS] 2025/07/25 - 19:52:14 | 正在更新数据看板数据... 
[SYS] 2025/07/25 - 19:52:14 | 保存数据看板数据成功，共保存1条数据 
[SYS] 2025/07/25 - 19:52:14 | syncing options from database 
[SYS] 2025/07/25 - 19:52:14 | syncing channels from database 
[SYS] 2025/07/25 - 19:52:14 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:52:14 | channels synced from database 
[SYS] 2025/07/25 - 19:52:14 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:52:14 | batch update started 
[SYS] 2025/07/25 - 19:52:14 | batch update finished 
[INFO] 2025/07/25 - 19:52:14 | 2025072519521196367931499MhMXzp | record consume log: userId=1, params={"channel_id":125,"prompt_tokens":1109,"completion_tokens":9,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988941509,"use_time_seconds":3,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["125"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"deepseek-v3","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:14 | 2025072519521196367931499MhMXzp | 200 |  2.734424197s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:15 | 20250725195212367883656CBrOx6n | record consume log: userId=1, params={"channel_id":124,"prompt_tokens":1409,"completion_tokens":44,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988941509,"use_time_seconds":3,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["124"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:15 | 20250725195212367883656CBrOx6n | 200 |  3.056121278s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:15 | 20250725195211997999082iHoFZYNR | record consume log: userId=1, params={"channel_id":124,"prompt_tokens":1409,"completion_tokens":36,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988941509,"use_time_seconds":3,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["124"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:15 | 20250725195211997999082iHoFZYNR | 200 |  3.428378698s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:52:16 | 2025072519521223512851o1stHoF0 | record consume log: userId=1, params={"channel_id":125,"prompt_tokens":1171,"completion_tokens":42,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988941509,"use_time_seconds":4,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["125"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"deepseek-v3","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:52:16 | 2025072519521223512851o1stHoF0 | 200 |  4.410135435s |      ********** |    POST /v1/chat/completions
[SYS] 2025/07/25 - 19:52:19 | batch update started 
[SYS] 2025/07/25 - 19:52:19 | batch update finished 
[GIN] 2025/07/25 - 19:52:19 | 20250725195219665050742TteAx1Mr | 200 |    1.766805ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:52:29 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:52:29 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:52:44 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:52:44 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:52:49 | 202507251952497287666050dAW5loE | 200 |    7.451517ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:52:59 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:52:59 | 任务进度轮询完成 
