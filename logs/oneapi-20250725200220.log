[SYS] 2025/07/25 - 20:02:20 | initializing token encoders 
[SYS] 2025/07/25 - 20:02:20 | token encoders initialized 
[SYS] 2025/07/25 - 20:02:20 | using MySQL as database 
[SYS] 2025/07/25 - 20:02:20 | database migration started 
[SYS] 2025/07/25 - 20:02:20 | database migrated 
[SYS] 2025/07/25 - 20:02:20 | system is already initialized at: 2025-06-29 19:19:27 +0800 CST 
[SYS] 2025/07/25 - 20:02:20 | Redis is enabled 
[SYS] 2025/07/25 - 20:02:20 | New API v0.8.8.0-alpha.5 started 
[SYS] 2025/07/25 - 20:02:20 | memory cache enabled 
[SYS] 2025/07/25 - 20:02:20 | sync frequency: 60 seconds 
[SYS] 2025/07/25 - 20:02:20 | channels synced from database 
[SYS] 2025/07/25 - 20:02:20 | batch update enabled with interval 5s 
[SYS] 2025/07/25 - 20:02:20 | 正在更新数据看板数据... 
[SYS] 2025/07/25 - 20:02:20 | 保存数据看板数据成功，共保存0条数据 
[SYS] 2025/07/25 - 20:02:20 | FRONTEND_BASE_URL is ignored on master node 
[GIN] 2025/07/25 - 20:02:25 | 20250725200225220901268qcsDW0dJ | 200 |   14.560826ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:02:35 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:02:35 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:02:48 | 20250725200248106752812XlTXwXhF | 200 |    2.226618ms |      172.19.0.1 |     GET /
[SYS] 2025/07/25 - 20:02:50 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:02:50 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:02:55 | 20250725200255301551510ubOWwRx3 | 200 |    1.942731ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:03:05 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:03:05 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:03:10 | 20250725200310447990130dP6rAu8w | 200 |   27.405573ms |      172.19.0.1 |     GET /
[GIN] 2025/07/25 - 20:03:11 | 2025072520031152911086qjlO5Gyu | 200 |    1.428136ms |      172.19.0.1 |     GET /api/status
[SYS] 2025/07/25 - 20:03:20 | syncing options from database 
[SYS] 2025/07/25 - 20:03:20 | syncing channels from database 
[SYS] 2025/07/25 - 20:03:20 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:03:20 | channels synced from database 
[SYS] 2025/07/25 - 20:03:20 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:03:22 | 20250725200322862633990VkHinqPL | 200 |    1.840211ms |      172.19.0.1 |    HEAD /assets/index-BbPB7pPU.js
[GIN] 2025/07/25 - 20:03:25 | 20250725200325351792587E0l29mxJ | 200 |    9.848298ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 20:03:35 | 202507252003351546707131rbtG6sG | 200 |    1.797314ms |      172.19.0.1 |     GET /api/status
[SYS] 2025/07/25 - 20:03:35 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:03:35 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:03:50 | 20250725200350227047617Zm1YFPX0 | 200 |   40.434886ms |      172.19.0.1 |     GET /
[GIN] 2025/07/25 - 20:03:50 | 202507252003503598931751iOrmUqU | 200 |    1.512889ms |      172.19.0.1 |     GET /api/status
[GIN] 2025/07/25 - 20:03:50 | 20250725200350412452896SMQdoczd | 200 |       658.2µs |      172.19.0.1 |    HEAD /assets/index-BbPB7pPU.js
[SYS] 2025/07/25 - 20:03:50 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:03:50 | 任务进度轮询完成 
[ERR] 2025/07/25 - 20:03:50 | 20250725200350518194956NrbAmxp8 | user 0 | 未提供令牌 
[GIN] 2025/07/25 - 20:03:50 | 20250725200350518194956NrbAmxp8 | 401 |     185.099µs |      172.19.0.1 |     GET /v1/models
[GIN] 2025/07/25 - 20:03:55 | 20250725200355424228524sPn6KD72 | 200 |     7.58986ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:04:05 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:04:05 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:04:20 | syncing options from database 
[SYS] 2025/07/25 - 20:04:20 | syncing channels from database 
[SYS] 2025/07/25 - 20:04:20 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:04:20 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:04:20 | channels synced from database 
[GIN] 2025/07/25 - 20:04:25 | 20250725200425494138523bGnaPrYz | 200 |    8.915946ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:04:35 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:04:35 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:04:50 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:04:50 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:04:55 | 20250725200455561301550wdEP1m8h | 200 |   15.008383ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:05:05 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:05:05 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:05:20 | syncing options from database 
[SYS] 2025/07/25 - 20:05:20 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:05:20 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:05:20 | syncing channels from database 
[SYS] 2025/07/25 - 20:05:20 | channels synced from database 
[GIN] 2025/07/25 - 20:05:25 | 20250725200525635224794TKSL54Uv | 200 |     2.56696ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:05:35 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:05:35 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:05:50 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:05:50 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:05:55 | 20250725200555696806454RzGItrtV | 200 |   79.600424ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:06:05 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:06:05 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:06:20 | syncing options from database 
[SYS] 2025/07/25 - 20:06:20 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:06:20 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:06:20 | syncing channels from database 
[SYS] 2025/07/25 - 20:06:20 | channels synced from database 
[GIN] 2025/07/25 - 20:06:25 | 20250725200625841550593kQTwPTtq | 200 |   10.740665ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:06:35 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:06:35 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:06:50 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:06:50 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:06:55 | 20250725200655912202991L2W1eZnH | 200 |   34.128924ms |             ::1 |     GET /api/status
