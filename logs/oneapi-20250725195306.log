[SYS] 2025/07/25 - 19:53:06 | initializing token encoders 
[SYS] 2025/07/25 - 19:53:06 | token encoders initialized 
[SYS] 2025/07/25 - 19:53:06 | using MySQL as database 
[SYS] 2025/07/25 - 19:53:06 | database migration started 
[SYS] 2025/07/25 - 19:53:07 | database migrated 
[SYS] 2025/07/25 - 19:53:07 | system is already initialized at: 2025-06-29 19:19:27 +0800 CST 
[SYS] 2025/07/25 - 19:53:07 | Redis is enabled 
[SYS] 2025/07/25 - 19:53:07 | New API v0.8.8.0-alpha.5 started 
[SYS] 2025/07/25 - 19:53:07 | memory cache enabled 
[SYS] 2025/07/25 - 19:53:07 | sync frequency: 60 seconds 
[SYS] 2025/07/25 - 19:53:07 | channels synced from database 
[SYS] 2025/07/25 - 19:53:07 | batch update enabled with interval 5s 
[SYS] 2025/07/25 - 19:53:07 | 正在更新数据看板数据... 
[SYS] 2025/07/25 - 19:53:07 | 保存数据看板数据成功，共保存0条数据 
[SYS] 2025/07/25 - 19:53:07 | FRONTEND_BASE_URL is ignored on master node 
[GIN] 2025/07/25 - 19:53:11 | 20250725195311891467179blYcoa6A | 200 |   37.124904ms |             ::1 |     GET /api/status
[INFO] 2025/07/25 - 19:53:21 | 20250725195320989020061TJ7JH2KF | user 1 with unlimited token has enough quota ＄19977.833018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:53:21 | 20250725195320989020059ROeYUbmD | user 1 with unlimited token has enough quota ＄19977.833018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:53:22 | 20250725195320989020059ROeYUbmD | record consume log: userId=1, params={"channel_id":128,"prompt_tokens":1157,"completion_tokens":9,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988916509,"use_time_seconds":1,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["128"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"translate-model-fast","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:53:22 | 20250725195320989020059ROeYUbmD | 200 |  1.095347259s |      ********** |    POST /v1/chat/completions
[SYS] 2025/07/25 - 19:53:22 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:53:22 | batch update started 
[SYS] 2025/07/25 - 19:53:22 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:53:22 | batch update finished 
[INFO] 2025/07/25 - 19:53:22 | 20250725195320989020061TJ7JH2KF | record consume log: userId=1, params={"channel_id":124,"prompt_tokens":1343,"completion_tokens":9,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988916509,"use_time_seconds":2,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["124"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:53:22 | 20250725195320989020061TJ7JH2KF | 200 |  1.253615163s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:53:22 | 20250725195322496248949Kj7HiDVT | user 1 with unlimited token has enough quota ＄19977.813018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:53:22 | 20250725195322496248949Kj7HiDVT | record error log: userId=1, channelId=124, modelName=gpt-4.1-mini, tokenName=Translate, content=您已达到请求数限制：1分钟内最多请求100次 (request id: 20250725195322602956376xYtQ0JE6) 
[GIN] 2025/07/25 - 19:53:22 | 20250725195322496248949Kj7HiDVT | 429 |  196.342051ms |      ********** |    POST /v1/chat/completions
[ERR] 2025/07/25 - 19:53:22 | 20250725195322496248949Kj7HiDVT | relay error (channel #124, status code: 429): 您已达到请求数限制：1分钟内最多请求100次 (request id: 20250725195322602956376xYtQ0JE6) (request id: 20250725195322496248949Kj7HiDVT) 
[INFO] 2025/07/25 - 19:53:23 | 20250725195323948047303Ft68hYR5 | user 1 with unlimited token has enough quota ＄19977.813018, trusted and no need to pre-consume 
[SYS] 2025/07/25 - 19:53:27 | batch update started 
[SYS] 2025/07/25 - 19:53:27 | batch update finished 
[INFO] 2025/07/25 - 19:53:27 | 20250725195323948047303Ft68hYR5 | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1317,"completion_tokens":11,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988906509,"use_time_seconds":4,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:53:27 | 20250725195323948047303Ft68hYR5 | 200 |  3.239306244s |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:53:27 | 20250725195327438583651Je6ie24h | user 1 with unlimited token has enough quota ＄19977.803018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:53:30 | 20250725195327438583651Je6ie24h | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1317,"completion_tokens":11,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988901509,"use_time_seconds":3,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:53:30 | 20250725195327438583651Je6ie24h | 200 |  2.702502398s |      ********** |    POST /v1/chat/completions
[SYS] 2025/07/25 - 19:53:32 | batch update started 
[SYS] 2025/07/25 - 19:53:32 | batch update finished 
[GIN] 2025/07/25 - 19:53:33 | 202507251953339441894395GxHCpXl | 200 |    2.024249ms |      ********** |     GET /api/status
[SYS] 2025/07/25 - 19:53:37 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:53:37 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:53:42 | 202507251953423732129x6K5ky7y | 200 |    8.614065ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:53:52 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:53:52 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:54:07 | syncing options from database 
[SYS] 2025/07/25 - 19:54:07 | syncing channels from database 
[SYS] 2025/07/25 - 19:54:07 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:54:07 | channels synced from database 
[SYS] 2025/07/25 - 19:54:07 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:54:12 | 2025072519541263601357uov4ZaHh | 200 |   17.784844ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:54:22 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:54:22 | 任务进度轮询完成 
[INFO] 2025/07/25 - 19:54:32 | 20250725195432859846338KLgiIL6o | user 1 with unlimited token has enough quota ＄19977.793018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:54:33 | 20250725195432859846338KLgiIL6o | record consume log: userId=1, params={"channel_id":128,"prompt_tokens":1281,"completion_tokens":58,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988896509,"use_time_seconds":1,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["128"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"translate-model-fast","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:54:33 | 20250725195432859846338KLgiIL6o | 200 |  571.961413ms |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:54:33 | 202507251954336843865247kIT4mTI | user 1 with unlimited token has enough quota ＄19977.783018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:54:34 | 202507251954336843865247kIT4mTI | record consume log: userId=1, params={"channel_id":128,"prompt_tokens":1197,"completion_tokens":20,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988891509,"use_time_seconds":1,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["128"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"is_model_mapped":true,"model_price":0.01,"model_ratio":0,"upstream_model_name":"translate-model-fast","user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:54:34 | 202507251954336843865247kIT4mTI | 200 |  624.842305ms |      ********** |    POST /v1/chat/completions
[INFO] 2025/07/25 - 19:54:34 | 20250725195434564521725dEpDfhHJ | user 1 with unlimited token has enough quota ＄19977.773018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:54:36 | 20250725195434564521725dEpDfhHJ | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1361,"completion_tokens":19,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988886509,"use_time_seconds":2,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:54:36 | 20250725195434564521725dEpDfhHJ | 200 |  2.372146563s |      ********** |    POST /v1/chat/completions
[SYS] 2025/07/25 - 19:54:37 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:54:37 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:54:37 | batch update started 
[INFO] 2025/07/25 - 19:54:37 | 20250725195437188196827KQ1KMJKg | user 1 with unlimited token has enough quota ＄19977.763018, trusted and no need to pre-consume 
[SYS] 2025/07/25 - 19:54:37 | batch update finished 
[INFO] 2025/07/25 - 19:54:37 | 20250725195437188196827KQ1KMJKg | record error log: userId=1, channelId=124, modelName=gpt-4.1-mini, tokenName=Translate, content=您已达到请求数限制：1分钟内最多请求100次 (request id: 20250725195437303632559AopeTmU4) 
[GIN] 2025/07/25 - 19:54:37 | 20250725195437188196827KQ1KMJKg | 429 |  211.062247ms |      ********** |    POST /v1/chat/completions
[ERR] 2025/07/25 - 19:54:37 | 20250725195437188196827KQ1KMJKg | relay error (channel #124, status code: 429): 您已达到请求数限制：1分钟内最多请求100次 (request id: 20250725195437303632559AopeTmU4) (request id: 20250725195437188196827KQ1KMJKg) 
[INFO] 2025/07/25 - 19:54:38 | 20250725195438661778841vDwuIAPh | user 1 with unlimited token has enough quota ＄19977.763018, trusted and no need to pre-consume 
[INFO] 2025/07/25 - 19:54:40 | 20250725195438661778841vDwuIAPh | record consume log: userId=1, params={"channel_id":126,"prompt_tokens":1361,"completion_tokens":19,"model_name":"gpt-4.1-mini","token_name":"Translate","quota":5000,"content":"模型价格 0.01，分组倍率 1.00","token_id":2,"user_quota":9988881509,"use_time_seconds":2,"is_stream":false,"group":"vip","other":{"admin_info":{"use_channel":["126"]},"cache_ratio":0,"cache_tokens":0,"completion_ratio":0,"frt":-1000,"group_ratio":1,"model_price":0.01,"model_ratio":0,"user_group_ratio":-1}} 
[GIN] 2025/07/25 - 19:54:40 | 20250725195438661778841vDwuIAPh | 200 |  1.784263415s |      ********** |    POST /v1/chat/completions
[GIN] 2025/07/25 - 19:54:42 | 20250725195442149220213wXWw4fQ9 | 200 |    1.681416ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:54:42 | batch update started 
[SYS] 2025/07/25 - 19:54:42 | batch update finished 
[SYS] 2025/07/25 - 19:54:52 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:54:52 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:55:04 | 20250725195504888517235nHFiD4fx | 200 |   33.769905ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:55:05 | 20250725195505477454697MfKqCAHj | 200 |     1.84372ms |      ********** |     GET /api/status
[SYS] 2025/07/25 - 19:55:07 | syncing options from database 
[SYS] 2025/07/25 - 19:55:07 | syncing channels from database 
[SYS] 2025/07/25 - 19:55:07 | channels synced from database 
[SYS] 2025/07/25 - 19:55:07 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:55:07 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:55:09 | 20250725195509473802291CrmSGHyu | 200 |    2.001593ms |      ********** |     GET /api/notice
[GIN] 2025/07/25 - 19:55:09 | 20250725195509474006284fuj3TrOd | 200 |    2.107603ms |      ********** |     GET /api/home_page_content
[GIN] 2025/07/25 - 19:55:11 | 20250725195511697752142HZGn8Sh1 | 200 |   17.316508ms |      ********** |     GET /api/data/?username=&start_timestamp=1753358112&end_timestamp=1753448112&default_time=hour
[GIN] 2025/07/25 - 19:55:11 | 20250725195511697752137dQIVJDaG | 200 |   22.473875ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:55:11 | 20250725195511967310613jZFsHKl1 | 200 |      720.63µs |      ********** |     GET /api/uptime/status
[GIN] 2025/07/25 - 19:55:12 | 20250725195512208333336Rn6Ji2Rd | 200 |     629.065µs |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:55:12 | 20250725195512764840558XtvHMmBM | 200 |   20.091957ms |      ********** |     GET /api/pricing
[GIN] 2025/07/25 - 19:55:18 | 20250725195518312848257grg6nM0n | 200 |    1.594698ms |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:55:21 | 202507251955216566133080xZrJfIA | 200 |    2.980019ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:55:21 | 20250725195521656616766yvSHbIB4 | 200 |     3.44021ms |      ********** |     GET /api/data/?username=&start_timestamp=1753358122&end_timestamp=1753448122&default_time=hour
[GIN] 2025/07/25 - 19:55:21 | 20250725195521907978454hKQRMdYJ | 200 |     606.216µs |      ********** |     GET /api/uptime/status
[SYS] 2025/07/25 - 19:55:22 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:55:22 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:55:23 | 2025072519552351100593703bOHzt6 | 200 |    6.879247ms |      ********** |     GET /api/pricing
[GIN] 2025/07/25 - 19:55:24 | 20250725195524393803993PlRnL4Fv | 200 |    2.677265ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:55:24 | 20250725195524393986166qpJDqc28 | 200 |    3.656415ms |      ********** |     GET /api/data/?username=&start_timestamp=1753358125&end_timestamp=1753448125&default_time=hour
[GIN] 2025/07/25 - 19:55:24 | 202507251955246513250353JYQpncW | 200 |     556.164µs |      ********** |     GET /api/uptime/status
[GIN] 2025/07/25 - 19:55:25 | 20250725195525781452880Erx0i7yN | 200 |    4.700147ms |      ********** |     GET /api/pricing
[GIN] 2025/07/25 - 19:55:27 | 20250725195527769789715w8Hviszh | 200 |    1.831333ms |      ********** |     GET /assets/index-D-BpiK38.js
[GIN] 2025/07/25 - 19:55:28 | 2025072519552821538122Aw2Twjx7 | 200 |    1.600597ms |      ********** |     GET /api/about
[GIN] 2025/07/25 - 19:55:30 | 20250725195530869397603GDAA0G09 | 200 |      1.8262ms |      ********** |     GET /api/notice
[GIN] 2025/07/25 - 19:55:30 | 20250725195530869490279JRpfsJNQ | 200 |    2.298032ms |      ********** |     GET /api/home_page_content
[GIN] 2025/07/25 - 19:55:32 | 20250725195532436807920pl8GKR8f | 200 |    3.653102ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:55:32 | 20250725195532437109971YbpLCiC9 | 200 |    4.154012ms |      ********** |     GET /api/data/?username=&start_timestamp=1753358133&end_timestamp=1753448133&default_time=hour
[GIN] 2025/07/25 - 19:55:32 | 20250725195532693606061eZnS4K77 | 200 |      633.94µs |      ********** |     GET /api/uptime/status
[GIN] 2025/07/25 - 19:55:35 | 20250725195535329841565ApoGlO9s | 200 |     3.93492ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:55:35 | 20250725195535329841562LvmET9jg | 200 |      9.1176ms |      ********** |     GET /api/user/models
[SYS] 2025/07/25 - 19:55:37 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:55:37 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:55:38 | 2025072519553837266647nFqt6RDr | 200 |    4.553125ms |      ********** |     GET /api/option/
[GIN] 2025/07/25 - 19:55:38 | 20250725195538754413505ipYDnOHW | 200 |    1.002217ms |      ********** |     GET /api/group/
[GIN] 2025/07/25 - 19:55:38 | 20250725195538754060804QQgiqRGQ | 200 |    2.210015ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:55:38 | 20250725195538754269420IvJkpngd | 200 |    4.977913ms |      ********** |     GET /api/user/?p=0&page_size=10
[GIN] 2025/07/25 - 19:55:42 | 20250725195542283332835bUZD9UCj | 200 |    4.237457ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:55:45 | 202507251955453360003519vSAhHZD | 200 |    3.988482ms |      ********** |    POST /api/user/manage
[GIN] 2025/07/25 - 19:55:50 | 20250725195550214150861goAho5Df | 200 |    3.307293ms |      ********** |     GET /api/channel/models
[GIN] 2025/07/25 - 19:55:50 | 20250725195550214988547EibnobG1 | 200 |    3.136607ms |      ********** |     GET /api/group/
[GIN] 2025/07/25 - 19:55:50 | 20250725195550215071232AW0MbUM5 | 200 |    3.082951ms |      ********** |     GET /api/models
[GIN] 2025/07/25 - 19:55:50 | 20250725195550214173106iw1kh8of | 200 |    8.265594ms |      ********** |     GET /api/channel/?p=1&page_size=10&id_sort=false&tag_mode=false
[SYS] 2025/07/25 - 19:55:52 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:55:52 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:55:56 | 20250725195556507769233vhmEC9Y3 | 200 |    5.007595ms |      ********** |     GET /api/channel/?p=1&page_size=10&id_sort=true&tag_mode=false
[GIN] 2025/07/25 - 19:55:58 | 20250725195558377557732FGHibRLL | 200 |    8.444648ms |      ********** |     GET /api/channel/?p=1&page_size=10&id_sort=true&tag_mode=true
[GIN] 2025/07/25 - 19:56:02 | 20250725195602676824940H5pzTfdT | 200 |    7.277209ms |      ********** |     GET /api/channel/?p=1&page_size=100&id_sort=true&tag_mode=true
[SYS] 2025/07/25 - 19:56:07 | syncing options from database 
[SYS] 2025/07/25 - 19:56:07 | syncing channels from database 
[SYS] 2025/07/25 - 19:56:07 | channels synced from database 
[SYS] 2025/07/25 - 19:56:07 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:56:07 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:56:12 | 20250725195612344325649fRw3Ccqs | 200 |    1.908841ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:56:15 | 20250725195615447883159RFSXJph5 | 200 |    2.211656ms |      ********** |     GET /console/channel
[GIN] 2025/07/25 - 19:56:15 | 20250725195615719654032Amveqi6q | 200 |   10.929265ms |      ********** |     GET /assets/tools-D64yHnf1.js
[GIN] 2025/07/25 - 19:56:15 | 20250725195615725877738sum4kqC4 | 200 |   39.892478ms |      ********** |     GET /assets/i18n-BSxjx2rs.js
[GIN] 2025/07/25 - 19:56:15 | 202507251956157197959677ecsLuVt | 200 |   54.276116ms |      ********** |     GET /assets/semi-ui-yRXI6evF.css
[GIN] 2025/07/25 - 19:56:15 | 202507251956157262194165TMTNoXR | 200 |   78.167443ms |      ********** |     GET /assets/react-components-XvIrFW7f.js
[GIN] 2025/07/25 - 19:56:15 | 202507251956157199908705Cfe6X5K | 200 |   92.511469ms |      ********** |     GET /assets/react-core-DtMAePju.js
[GIN] 2025/07/25 - 19:56:15 | 20250725195615719898245vPYgZC5l | 200 |  128.653373ms |      ********** |     GET /assets/index-C8L2jEvp.css
[GIN] 2025/07/25 - 19:56:15 | 202507251956157207014208vtIjlvP | 200 |  223.621445ms |      ********** |     GET /assets/semi-ui-BN13Wcpw.js
[GIN] 2025/07/25 - 19:56:16 | 202507251956157185075649uZLNWwF | 200 |   284.91974ms |      ********** |     GET /assets/index-BbPB7pPU.js
[GIN] 2025/07/25 - 19:56:17 | 20250725195617746226046eX5yNBSw | 200 |     1.69356ms |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:56:17 | 20250725195617746231141trVJlBr6 | 200 |    3.045063ms |      ********** |     GET /logo.png
[SYS] 2025/07/25 - 19:56:22 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:56:22 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:56:24 | 20250725195624122449787EM6wurqI | 200 |    1.835336ms |      ********** |     GET /api/notice
[GIN] 2025/07/25 - 19:56:24 | 20250725195624122389917d4upOkBL | 200 |    3.048218ms |      ********** |     GET /api/home_page_content
[GIN] 2025/07/25 - 19:56:26 | 20250725195626375547231L56z8Esj | 200 |    4.196525ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:56:26 | 20250725195626376411233tZOAKPTq | 200 |    6.409412ms |      ********** |     GET /api/data/?username=&start_timestamp=1753358187&end_timestamp=1753448187&default_time=hour
[GIN] 2025/07/25 - 19:56:26 | 202507251956266331049343eFI8pBZ | 200 |     645.698µs |      ********** |     GET /api/uptime/status
[GIN] 2025/07/25 - 19:56:27 | 20250725195627329484956pWnXn8IW | 200 |   16.435206ms |      ********** |     GET /api/pricing
[GIN] 2025/07/25 - 19:56:30 | 202507251956307541765189YPYgasz | 200 |    1.676018ms |      ********** |     GET /api/home_page_content
[GIN] 2025/07/25 - 19:56:30 | 202507251956307541765160jbAtd8g | 200 |    2.566109ms |      ********** |     GET /api/notice
[SYS] 2025/07/25 - 19:56:37 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:56:37 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:56:42 | 202507251956424166277590SGKpncG | 200 |    2.729514ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:56:52 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:56:52 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:56:53 | 20250725195653764821810Blv03UY0 | 200 |    1.966663ms |      ********** |     GET /
[GIN] 2025/07/25 - 19:56:54 | 2025072519565433268133nzyEodjv | 200 |    9.320972ms |      ********** |     GET /assets/react-core-DtMAePju.js
[GIN] 2025/07/25 - 19:56:54 | 2025072519565443401985PMCvq7yq | 200 |    3.563273ms |      ********** |     GET /assets/i18n-BSxjx2rs.js
[GIN] 2025/07/25 - 19:56:54 | 2025072519565447542835a5XFkaIZ | 200 |   38.360208ms |      ********** |     GET /assets/semi-ui-yRXI6evF.css
[GIN] 2025/07/25 - 19:56:54 | 2025072519565460534785Eyhl9967 | 200 |   42.143252ms |      ********** |     GET /assets/index-C8L2jEvp.css
[GIN] 2025/07/25 - 19:56:54 | 20250725195654100846876rWCD0TWg | 200 |    4.471747ms |      ********** |     GET /assets/tools-D64yHnf1.js
[GIN] 2025/07/25 - 19:56:54 | 20250725195654105673255rs5KwL8m | 200 |    2.054363ms |      ********** |     GET /assets/react-components-XvIrFW7f.js
[GIN] 2025/07/25 - 19:56:54 | 2025072519565433213364VcKMOeC9 | 200 |  120.203079ms |      ********** |     GET /assets/index-BbPB7pPU.js
[GIN] 2025/07/25 - 19:56:54 | 20250725195654806992802Zq5W9d4 | 200 |   94.857287ms |      ********** |     GET /assets/semi-ui-BN13Wcpw.js
[GIN] 2025/07/25 - 19:56:55 | 20250725195655637695701CbGzJ6WD | 200 |    3.534951ms |      ********** |     GET /assets/index-BAeIAZO0.js
[GIN] 2025/07/25 - 19:56:55 | 20250725195655641637776P3FxU2jV | 200 |    1.087933ms |      ********** |     GET /logo.png
[GIN] 2025/07/25 - 19:56:55 | 202507251956556418002418QiGryjA | 200 |     1.47382ms |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:56:55 | 20250725195655900244091E0OpJfFm | 200 |    1.558121ms |      ********** |     GET /logo.png
[GIN] 2025/07/25 - 19:56:55 | 20250725195655912274557PlMS05T0 | 200 |     662.943µs |      ********** |     GET /api/home_page_content
[GIN] 2025/07/25 - 19:56:55 | 20250725195655912450192bofUVj92 | 200 |     967.051µs |      ********** |     GET /api/notice
[GIN] 2025/07/25 - 19:56:57 | 20250725195657764043696Z9HGXnl4 | 200 |    2.584614ms |      ********** |     GET /api/data/?username=&start_timestamp=1753358218&end_timestamp=1753448218&default_time=hour
[GIN] 2025/07/25 - 19:56:57 | 20250725195657764043673aq45ufLk | 200 |     8.99222ms |      ********** |     GET /api/user/self
[GIN] 2025/07/25 - 19:56:58 | 20250725195658152355581vxiuCQY | 200 |    1.640193ms |      ********** |     GET /api/uptime/status
[GIN] 2025/07/25 - 19:57:00 | 20250725195700464483934TaWqAwHh | 200 |    1.999952ms |      ********** |     GET /console
[GIN] 2025/07/25 - 19:57:00 | 2025072519570075060343945iLeLGZ | 200 |   17.723901ms |      ********** |     GET /assets/tools-D64yHnf1.js
[GIN] 2025/07/25 - 19:57:00 | 202507251957007498640509R8iGTww | 200 |   32.810122ms |      ********** |     GET /assets/semi-ui-yRXI6evF.css
[GIN] 2025/07/25 - 19:57:00 | 20250725195700750831848okufFOU3 | 200 |    34.34096ms |      ********** |     GET /assets/react-components-XvIrFW7f.js
[GIN] 2025/07/25 - 19:57:00 | 20250725195700749606313SWihwNL0 | 200 |    39.50676ms |      ********** |     GET /assets/i18n-BSxjx2rs.js
[GIN] 2025/07/25 - 19:57:00 | 20250725195700750055220hAo08oju | 200 |   44.169399ms |      ********** |     GET /assets/index-C8L2jEvp.css
[GIN] 2025/07/25 - 19:57:00 | 20250725195700750238862h2GB23ro | 200 |   65.545881ms |      ********** |     GET /assets/react-core-DtMAePju.js
[GIN] 2025/07/25 - 19:57:00 | 20250725195700738165205TVuBa8SQ | 200 |  116.388465ms |      ********** |     GET /assets/index-BbPB7pPU.js
[GIN] 2025/07/25 - 19:57:00 | 20250725195700750411652OD65PBQr | 200 |  128.549067ms |      ********** |     GET /assets/semi-ui-BN13Wcpw.js
[GIN] 2025/07/25 - 19:57:02 | 20250725195702271630653hJqfMCSl | 200 |    2.942896ms |      ********** |     GET /api/status
[GIN] 2025/07/25 - 19:57:02 | 20250725195702271875479NeqM4vGn | 200 |    3.519638ms |      ********** |     GET /logo.png
[SYS] 2025/07/25 - 19:57:07 | syncing options from database 
[SYS] 2025/07/25 - 19:57:07 | syncing channels from database 
[SYS] 2025/07/25 - 19:57:07 | channels synced from database 
[SYS] 2025/07/25 - 19:57:07 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:57:07 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:57:12 | 20250725195712494993919px1uaYqE | 200 |     2.60984ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 19:57:21 | 20250725195721637205221WVjR1ght | 200 |     1.65356ms |      ********** |     GET /
[SYS] 2025/07/25 - 19:57:22 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:57:22 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:57:32 | 20250725195732157784349bGBwICmu | 200 |    1.587226ms |      ********** |     GET /
[SYS] 2025/07/25 - 19:57:37 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:57:37 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:57:42 | 20250725195742555157866WTovZA9t | 200 |    1.582914ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:57:52 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:57:52 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:58:07 | 正在更新数据看板数据... 
[SYS] 2025/07/25 - 19:58:07 | 保存数据看板数据成功，共保存1条数据 
[SYS] 2025/07/25 - 19:58:07 | syncing options from database 
[SYS] 2025/07/25 - 19:58:07 | syncing channels from database 
[SYS] 2025/07/25 - 19:58:07 | channels synced from database 
[SYS] 2025/07/25 - 19:58:07 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:58:07 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:58:12 | 20250725195812620873044bVJ8ZD8K | 200 |    7.749505ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 19:58:22 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:58:22 | 任务进度轮询完成 
[SYS] 2025/07/25 - 19:58:37 | 任务进度轮询开始 
[SYS] 2025/07/25 - 19:58:37 | 任务进度轮询完成 
[GIN] 2025/07/25 - 19:58:42 | 20250725195842687738335Pr3HPU6Z | 200 |   16.314112ms |             ::1 |     GET /api/status
