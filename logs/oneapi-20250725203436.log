[SYS] 2025/07/25 - 20:34:36 | initializing token encoders 
[SYS] 2025/07/25 - 20:34:36 | token encoders initialized 
[SYS] 2025/07/25 - 20:34:36 | using MySQL as database 
[SYS] 2025/07/25 - 20:34:36 | database migration started 
[SYS] 2025/07/25 - 20:34:36 | database migrated 
[SYS] 2025/07/25 - 20:34:36 | system is already initialized at: 2025-06-29 19:19:27 +0800 CST 
[SYS] 2025/07/25 - 20:34:36 | Redis is enabled 
[SYS] 2025/07/25 - 20:34:36 | New API v0.8.8.0-alpha.5 started 
[SYS] 2025/07/25 - 20:34:36 | memory cache enabled 
[SYS] 2025/07/25 - 20:34:36 | sync frequency: 60 seconds 
[SYS] 2025/07/25 - 20:34:36 | channels synced from database 
[SYS] 2025/07/25 - 20:34:36 | batch update enabled with interval 5s 
[SYS] 2025/07/25 - 20:34:36 | 正在更新数据看板数据... 
[SYS] 2025/07/25 - 20:34:36 | 保存数据看板数据成功，共保存0条数据 
[SYS] 2025/07/25 - 20:34:36 | FRONTEND_BASE_URL is ignored on master node 
[GIN] 2025/07/25 - 20:34:41 | 20250725203441316596111FcYTg1tk | 200 |      933.91µs |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:34:51 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:34:51 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:35:06 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:35:06 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:35:09 | 20250725203509113305356oLqBkceH | 200 |   19.574031ms |    ************ |     GET /ai
[GIN] 2025/07/25 - 20:35:11 | 20250725203511384356745DJXepP2y | 200 |    1.733127ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 20:35:18 | 20250725203518167944551iXPp2Brs | 200 |     3.37305ms |    ************ |     GET /ai
[SYS] 2025/07/25 - 20:35:21 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:35:21 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:35:28 | 2025072520352876112977gYr0yip3 | 200 |    1.406372ms |    ************ |     GET /ai
[SYS] 2025/07/25 - 20:35:36 | syncing options from database 
[SYS] 2025/07/25 - 20:35:36 | syncing channels from database 
[SYS] 2025/07/25 - 20:35:36 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:35:36 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:35:36 | channels synced from database 
[GIN] 2025/07/25 - 20:35:37 | 2025072520353719836779644LP2TV3 | 200 |    1.213321ms |    ************ |     GET /ai
[GIN] 2025/07/25 - 20:35:41 | 20250725203541448137078E0jcvPN7 | 200 |    1.724275ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:35:51 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:35:51 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:36:06 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:36:06 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:36:06 | 20250725203606896857909pupYjB2a | 200 |   30.637246ms | *************** |     GET /ai/console
[GIN] 2025/07/25 - 20:36:07 | 20250725203607468605433Cqd05KOX | 200 |    1.326668ms | *************** |     GET /api/status
[GIN] 2025/07/25 - 20:36:11 | 20250725203611517260261yPpDUtHv | 200 |    1.843015ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 20:36:15 | 20250725203615347880702veIFiXBw | 200 |    1.754677ms | *************** |     GET /ai/console
[GIN] 2025/07/25 - 20:36:15 | 20250725203615638318351L0my99Fx | 200 |    3.091221ms | *************** |     GET /assets/i18n-BSxjx2rs.js
[GIN] 2025/07/25 - 20:36:15 | 20250725203615642250838MlvNNIsw | 200 |    1.542277ms | *************** |     GET /assets/react-components-XvIrFW7f.js
[GIN] 2025/07/25 - 20:36:15 | 20250725203615623082136fuoY7G2O | 200 |   31.106312ms | *************** |     GET /assets/react-core-DtMAePju.js
[GIN] 2025/07/25 - 20:36:15 | 202507252036156221462843Hx3vD32 | 200 |   73.048091ms | *************** |     GET /assets/semi-ui-yRXI6evF.css
[GIN] 2025/07/25 - 20:36:15 | 2025072520361562314205148JGVSFm | 200 |   90.670882ms | *************** |     GET /assets/index-C8L2jEvp.css
[GIN] 2025/07/25 - 20:36:15 | 20250725203615623005162uM9gyL9B | 200 |   92.622609ms | *************** |     GET /assets/tools-D64yHnf1.js
[GIN] 2025/07/25 - 20:36:15 | 20250725203615621540344362zVMkG | 200 |  118.409929ms | *************** |     GET /assets/index-BbPB7pPU.js
[GIN] 2025/07/25 - 20:36:15 | 20250725203615622698257PLhne28f | 200 |  132.559596ms | *************** |     GET /assets/semi-ui-BN13Wcpw.js
[GIN] 2025/07/25 - 20:36:17 | 202507252036174064524777bJ1yvtP | 200 |    2.175443ms | *************** |     GET /api/status
[GIN] 2025/07/25 - 20:36:17 | 20250725203617406759049CYqVtUHx | 200 |      2.6098ms | *************** |     GET /logo.png
[GIN] 2025/07/25 - 20:36:17 | 2025072520361767858539773wE0xAp | 200 |    2.157258ms | *************** |     GET /logo.png
[GIN] 2025/07/25 - 20:36:19 | 202507252036194490246526k1gTIgi | 200 |    1.703821ms | *************** |     GET /ai/console
[GIN] 2025/07/25 - 20:36:19 | 20250725203619782873313QcLC5eOt | 200 |     873.528µs | *************** |     GET /api/status
[SYS] 2025/07/25 - 20:36:21 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:36:21 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:36:25 | 20250725203625786589683yhJsNthu | 200 |    2.092795ms |    ************ |     GET /ai/api/status
[SYS] 2025/07/25 - 20:36:36 | syncing options from database 
[SYS] 2025/07/25 - 20:36:36 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:36:36 | syncing channels from database 
[SYS] 2025/07/25 - 20:36:36 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:36:36 | channels synced from database 
[GIN] 2025/07/25 - 20:36:41 | 20250725203641581122442tK6KhlwM | 200 |    2.031053ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:36:51 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:36:51 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:37:06 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:37:06 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:37:11 | 20250725203711645034281oy0AjW5z | 200 |    14.85499ms |             ::1 |     GET /api/status
[SYS] 2025/07/25 - 20:37:21 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:37:21 | 任务进度轮询完成 
[SYS] 2025/07/25 - 20:37:36 | syncing options from database 
[SYS] 2025/07/25 - 20:37:36 | syncing channels from database 
[SYS] 2025/07/25 - 20:37:36 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:37:36 | channels synced from database 
[SYS] 2025/07/25 - 20:37:36 | 任务进度轮询完成 
[GIN] 2025/07/25 - 20:37:40 | 20250725203740648267396Xk6ReiIO | 200 |   19.721866ms |    ************ |     GET /api/status
[GIN] 2025/07/25 - 20:37:41 | 20250725203741716816654HGshH4ej | 200 |    1.610277ms |             ::1 |     GET /api/status
[GIN] 2025/07/25 - 20:37:50 | 20250725203750251766425kWUukMja | 200 |    1.814609ms |    ************ |     GET /ai
[SYS] 2025/07/25 - 20:37:51 | 任务进度轮询开始 
[SYS] 2025/07/25 - 20:37:51 | 任务进度轮询完成 
