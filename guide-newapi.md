# New-API 项目完整指南

## 📋 项目概述

这是一个基于 New-API 的定制化部署项目，支持多路径访问和完整的管理功能。

### 🌐 **域名和访问路径**
- **主域名**: `liangliangdamowang.edu.deal`
- **前端访问**:
  - 原始路径: `https://liangliangdamowang.edu.deal/`
  - AI路径: `https://liangliangdamowang.edu.deal/ai/` ✨ **新增功能**
- **API访问**:
  - 原始API: `https://liangliangdamowang.edu.deal/api/*`
  - 原始OpenAI: `https://liangliangdamowang.edu.deal/v1/*`
  - AI API: `https://liangliangdamowang.edu.deal/ai/api/*` ✨ **新增功能**
  - AI OpenAI: `https://liangliangdamowang.edu.deal/ai/v1/*` ✨ **新增功能**

## 🏗️ **项目架构**

### **Docker 容器架构**
```
nginx-proxy (nginx:alpine)
├── 端口: 80, 443
├── SSL: /etc/nginx/ssl/
└── 配置: /etc/nginx/conf.d/default.conf

new-api (calciumion/new-api:latest)
├── 端口: 3000 (内部)
├── 数据: ./data:/data
└── 日志: ./logs:/app/logs

mysql (mysql:8.2)
├── 端口: 3306 (内部)
├── 数据: mysql_data volume
└── 配置: 自定义优化参数

redis (redis:7-alpine)
├── 端口: 6379 (内部)
├── 数据: redis_data volume
└── 配置: 内存限制 256MB
```

### **文件系统结构**
```
/root/workspace/new-api/
├── docker-compose.yml          # Docker 编排配置
├── deploy-newapi.sh           # 一键部署脚本 ✨ 已更新
├── new-api-manager.sh         # 统一管理工具 ✨ 已更新
├── guide-newapi.md            # 项目完整指南 ✨ 新增
├── nginx-newapi/              # Nginx 配置目录
│   ├── default.conf           # Docker 容器配置 ✨ 已更新
│   ├── system-nginx.conf      # 系统 HTTPS 配置 ✨ 已更新
│   ├── system-nginx-http.conf # 系统 HTTP 配置 ✨ 已更新
│   ├── default.conf.backup    # 历史备份
│   └── default.conf.backup2   # 历史备份
├── web/                       # 前端项目
│   ├── dist/                  # 构建输出 ✨ 已更新
│   ├── src/                   # 源代码
│   ├── vite.config.js         # 构建配置 ✨ 已更新
│   └── package.json           # 依赖配置
├── scripts/                   # 管理脚本目录 ✨ 重新组织
│   ├── database/              # 数据库相关脚本
│   │   ├── manual-restore.sh  # 手动恢复工具
│   │   └── restore_database.sh # 恢复脚本（符号链接）
│   ├── deployment/            # 部署相关脚本
│   │   └── test-deployment.sh # 部署测试
│   ├── maintenance/           # 维护脚本
│   │   └── update-with-ai-path.sh # AI路径更新 ✨
│   ├── backup-database.sh     # 数据库备份
│   ├── restore-database.sh    # 数据库恢复
│   ├── auto-backup-manager.sh # 自动备份管理
│   ├── manage-docker-services.sh # Docker服务管理
│   ├── manage-services.sh     # 系统服务管理
│   ├── setup-autostart.sh     # 自启动配置
│   ├── renew-ssl.sh          # SSL证书续期
│   └── update-*.sh           # 各种更新脚本
├── data/                      # 应用数据
├── logs/                      # 日志文件
├── ssl/                       # SSL 证书
└── backup/                    # 数据库备份 ✨ 使用上海时区
```

## 🔧 **核心配置文件**

### **1. Docker Compose 配置**
- **文件**: `docker-compose.yml`
- **关键配置**:
  - 网络: `new-api-network`
  - 卷挂载: 数据、日志、SSL、前端文件 ✨
  - 健康检查: 所有服务
  - 环境变量: `.env` 文件

### **2. Nginx 配置** ✨ **已全面更新**

#### **Docker 容器配置** (`nginx-newapi/default.conf`)
- **用途**: Docker 环境中的 nginx 配置
- **特性**:
  - SSL 终止 (443 → 80 重定向)
  - AI 路径支持 (`/ai/`, `/ai/api/*`, `/ai/v1/*`)
  - 静态文件服务 (`/ai/assets/*`)
  - WebSocket 支持
  - 缓存策略

#### **系统级配置**
- **HTTPS**: `nginx-newapi/system-nginx.conf`
- **HTTP**: `nginx-newapi/system-nginx-http.conf`
- **用途**: 直接在系统 nginx 中使用
- **特性**: 与 Docker 配置功能一致

### **3. 前端配置** ✨ **已更新**

#### **构建配置** (`web/vite.config.js`)
```javascript
export default defineConfig({
  base: './',  // 相对路径，支持多路径部署
  // ... 其他配置
});
```

#### **路由配置** (`web/src/index.js`)
```javascript
<BrowserRouter
  basename="/ai"  // AI 路径的 basename
  // ... 其他配置
>
```

### **4. 后端路由** ✨ **已更新**

#### **主路由** (`router/main.go`)
- 添加了 `SetAIWebRouter` 调用
- 支持 `/ai` 路径的前端服务

#### **Web路由** (`router/web-router.go`)
- 新增 `SetAIWebRouter` 函数
- 处理 `/ai` 路径下的前端路由
- 排除 API 路径的前端处理

## 📦 **数据管理**

### **数据库备份**
- **自动备份**: `scripts/auto-backup-manager.sh`
- **手动备份**: `scripts/backup-database.sh`
- **备份位置**: `./backup/`
- **备份格式**: `database_backup_YYYYMMDD_HHMMSS.sql`

### **数据恢复**
- **脚本**: `scripts/restore-database.sh`
- **手动恢复**: `restore_database.sh`
- **支持**: 指定备份文件恢复

### **数据持久化**
- **MySQL**: `mysql_data` Docker volume
- **Redis**: `redis_data` Docker volume
- **应用数据**: `./data` 目录挂载
- **日志**: `./logs` 目录挂载

## 🚀 **新机器部署指南**

### **⚡ 快速部署（推荐）**

在全新的 Ubuntu/Debian 服务器上一键部署：

```bash
# 1. 更新系统
sudo apt update && sudo apt upgrade -y

# 2. 安装必要依赖
sudo apt install -y git curl wget docker.io docker-compose certbot

# 3. 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 4. 克隆项目
git clone <your-repository-url> /root/workspace/new-api
cd /root/workspace/new-api

# 5. 运行部署脚本
./deploy-newapi.sh your-domain.com
```

### **📋 部署前准备清单**

#### **服务器要求**：
- ✅ Ubuntu 18.04+ 或 Debian 10+
- ✅ 至少 2GB RAM
- ✅ 至少 20GB 磁盘空间
- ✅ 开放端口：80, 443, 22

#### **域名要求**：
- ✅ 域名已解析到服务器IP
- ✅ 确保80和443端口可访问
- ✅ 准备好管理员邮箱（用于SSL证书）

#### **项目文件检查**：
- ✅ `docker-compose.yml` - Docker编排配置
- ✅ `deploy-newapi.sh` - 部署脚本（可执行）
- ✅ `new-api-manager.sh` - 管理工具（可执行）
- ✅ `guide-newapi.md` - 本指南文件
- ✅ `nginx-newapi/default.conf` - Nginx配置
- ✅ `web/` - 前端源码目录
- ✅ `scripts/` - 管理脚本目录

#### **配置验证**：
```bash
# 检查nginx AI路径配置
grep "/ai/" nginx-newapi/default.conf

# 检查前端配置
grep "base.*'./'\\|base.*\"./\"" web/vite.config.js
grep 'basename.*"/ai"' web/src/index.js

# 检查脚本语法
bash -n deploy-newapi.sh
bash -n new-api-manager.sh
```

#### **可选准备**：
- 📁 数据库备份文件（放在 `backup/` 目录）
- ⚙️ 自定义 `.env` 配置文件
- 🔐 现有SSL证书文件

## 🚀 **部署和管理**

### **一键部署**

#### **新机器部署步骤**：
```bash
# 1. 克隆项目到新机器
git clone <repository-url> /root/workspace/new-api
cd /root/workspace/new-api

# 2. 确保Docker和Docker Compose已安装
sudo apt update
sudo apt install -y docker.io docker-compose

# 3. 运行一键部署脚本
./deploy-newapi.sh [domain]

# 示例：
./deploy-newapi.sh liangliangdamowang.edu.deal
```

#### **部署脚本功能**：
- ✅ 自动检查系统依赖（Docker、Docker Compose、certbot）
- ✅ 创建必要目录结构
- ✅ 自动生成 .env 配置文件
- ✅ 自动构建前端（如果需要）
- ✅ 申请和配置 SSL 证书
- ✅ 启动所有服务容器
- ✅ 验证部署状态
- ✅ 显示访问地址和管理命令

### **服务管理**
```bash
# 统一管理工具（推荐）
./new-api-manager.sh

# 直接命令行使用
./new-api-manager.sh [命令]
# 可用命令: status, start, stop, restart, logs, backup, restore, update, test, info, help

# 底层脚本
./scripts/manage-docker-services.sh [action]
./scripts/manage-services.sh [action]
```

### **更新管理** ✨ **AI路径兼容**
```bash
# AI路径兼容更新（推荐）
./scripts/maintenance/update-with-ai-path.sh

# 通过管理工具更新
./new-api-manager.sh update

# 传统更新脚本（可能不兼容AI路径）
./scripts/update-*.sh
```

## 🔄 **更新流程** ✨ **重要**

### **推荐更新方式**
使用专门的更新脚本：
```bash
./update-with-ai-path.sh
```

### **更新脚本功能**
1. **自动备份**所有自定义配置
2. **拉取最新**的 new-api 更新
3. **重新应用** AI 路径配置
4. **重新构建**前端应用
5. **重启服务**并验证

### **手动更新指南**
详见 `AI_PATH_UPDATE_GUIDE.md`

## ⚠️ **重要注意事项**

### **可以安全修改的文件**
- `docker-compose.yml` - Docker 配置
- `nginx-newapi/*.conf` - Nginx 配置
- `web/src/*` - 前端源代码
- `scripts/*` - 管理脚本
- `.env` - 环境变量

### **需要谨慎修改的文件**
- `router/*.go` - 后端路由（已有自定义修改）
- `web/vite.config.js` - 前端构建配置（已有自定义修改）
- `web/src/index.js` - 前端入口（已有自定义修改）

### **不应该修改的文件**
- `README.md` - 官方文档
- `main.go` - 主程序入口
- `common/*`, `controller/*`, `model/*` - 核心业务逻辑

## 🛠️ **故障排除**

### **部署阶段问题**

#### **1. 部署脚本失败**
```bash
# 检查系统依赖
docker --version
docker-compose --version
certbot --version

# 检查端口占用
sudo netstat -tlnp | grep :80
sudo netstat -tlnp | grep :443

# 手动停止冲突服务
sudo systemctl stop nginx
sudo systemctl stop apache2
```

#### **2. SSL证书申请失败**
```bash
# 检查域名解析
nslookup your-domain.com

# 手动申请证书
sudo certbot certonly --standalone -d your-domain.com

# 检查防火墙
sudo ufw status
sudo ufw allow 80
sudo ufw allow 443
```

#### **3. Docker容器启动失败**
```bash
# 查看容器状态
docker-compose ps

# 查看容器日志
docker-compose logs mysql
docker-compose logs redis
docker-compose logs new-api
docker-compose logs nginx

# 重新构建容器
docker-compose down
docker-compose up --build -d
```

### **运行阶段问题**

#### **1. AI 路径不工作**
```bash
# 检查 nginx 配置
docker logs nginx-proxy

# 检查前端文件挂载
docker exec nginx-proxy ls -la /var/www/ai/

# 重启服务
docker-compose restart nginx
```

#### **2. API 路径问题**
```bash
# 测试 AI API 路径
curl -k https://your-domain.com/ai/api/status

# 测试原始 API 路径
curl -k https://your-domain.com/api/status

# 检查API服务状态
docker-compose logs new-api | tail -50
```

#### **3. 前端构建问题**
```bash
cd web
npm install --legacy-peer-deps
DISABLE_ESLINT_PLUGIN='true' npm run build
cd ..
docker-compose restart nginx
```

#### **4. 数据库连接问题**
```bash
# 检查数据库状态
docker exec mysql mysql -u root -p123456 -e "SHOW DATABASES;"

# 重置数据库
docker-compose restart mysql
sleep 30
./scripts/database/manual-restore.sh
```

### **日志位置**
- **Nginx**: `./logs/nginx/`
- **New-API**: `./logs/oneapi-*.log`
- **MySQL**: `./logs/mysql/`
- **Redis**: `./logs/redis/`

## 📋 **功能特性总结**

### ✅ **已实现功能**
- **双路径访问**: 原始路径 + AI 路径
- **API 路径支持**: `/ai/api/*` 和 `/ai/v1/*`
- **向后兼容**: 原有路径继续可用
- **自动更新**: 专用更新脚本
- **完整备份**: 数据库和配置备份
- **SSL 支持**: Let's Encrypt 自动证书
- **容器化部署**: Docker Compose 编排
- **健康检查**: 所有服务监控

### 🎯 **设计原则**
- **兼容性优先**: 不破坏现有功能
- **配置分离**: 环境变量和配置文件
- **自动化管理**: 脚本化运维
- **安全性**: SSL、防火墙、权限控制
- **可维护性**: 清晰的文档和注释

## 📞 **技术支持**

### **相关文档**
- `guide-newapi.md` - 本文档，项目完整指南
- `README.md` - New-API 官方文档
- `update-with-ai-path.sh` - AI 路径更新脚本

### **脚本文件说明**

#### **✅ 无需修改的脚本**
以下脚本在 AI 路径功能添加后无需任何修改：

**数据库管理**:
- `scripts/backup-database.sh` - 数据库备份
- `scripts/restore-database.sh` - 数据库恢复
- `scripts/auto-backup-manager.sh` - 自动备份管理
- `restore_database.sh` - 手动恢复脚本

**服务管理**:
- `scripts/manage-docker-services.sh` - Docker 服务管理
- `scripts/manage-services.sh` - 系统服务管理
- `new-api-manager.sh` - 统一管理工具

**系统配置**:
- `scripts/setup-autostart.sh` - 自启动配置
- `scripts/renew-ssl.sh` - SSL 证书续期
- `nginx-proxy.service` - Nginx 服务配置
- `new-api.service` - New-API 服务配置

**更新脚本**:
- `scripts/update-new-api-core.sh` - 核心更新
- `scripts/update-official-image.sh` - 官方镜像更新
- `scripts/update-project.sh` - 项目更新
- `scripts/quick-update.sh` - 快速更新

**部署脚本**:
- `cmd.sh` - 一键部署脚本
- `deploy.sh` - 部署脚本
- `test-deployment.sh` - 部署测试

**原因**: 这些脚本主要处理数据库操作、服务管理、系统配置等，与前端路径配置无关。

### **配置参考**
所有配置文件都包含详细注释，说明各项配置的用途和注意事项。

## 🔐 **安全配置**

### **SSL 证书管理**
- **证书位置**: `./ssl/certificate.crt`, `./ssl/private.key`
- **自动续期**: `scripts/renew-ssl.sh`
- **Let's Encrypt**: 支持自动申请和续期
- **证书验证**: nginx 配置中的 SSL 参数优化

### **防火墙配置**
```bash
# 开放必要端口
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 22/tcp
```

### **访问控制**
- **管理员认证**: New-API 内置用户系统
- **API 密钥**: Token 管理系统
- **速率限制**: nginx 和应用层双重限制

## 🌍 **多环境支持**

### **环境变量配置** (`.env`)

#### **自动生成配置**：
部署脚本会自动创建基本的 `.env` 文件，包含：

```bash
# 自动生成的基本配置
MYSQL_ROOT_PASSWORD=123456
MYSQL_DATABASE=new-api
REDIS_CONN_STRING=redis://redis
SESSION_SECRET=<随机生成的32位密钥>
DOMAIN_NAME=<您的域名>
SSL_EMAIL=admin@<您的域名>
```

#### **完整配置选项**：
如需自定义，可手动创建 `.env` 文件：

```bash
# 域名和SSL配置
DOMAIN_NAME=liangliangdamowang.edu.deal
SSL_EMAIL=<EMAIL>

# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_password
MYSQL_DATABASE=new-api
MYSQL_USER=new-api
MYSQL_PASSWORD=your_mysql_password

# Redis 配置
REDIS_CONN_STRING=redis://redis:6379
REDIS_PASSWORD=your_redis_password

# 应用配置
SESSION_SECRET=your_32_character_secret_key
INITIAL_ROOT_TOKEN=your_initial_admin_token

# 可选配置
TZ=Asia/Shanghai
LOG_LEVEL=info
MAX_REQUEST_SIZE=20MB
```

#### **安全建议**：
- 🔐 修改默认的 `MYSQL_ROOT_PASSWORD`
- 🔑 使用强密码和随机的 `SESSION_SECRET`
- 🚫 不要将 `.env` 文件提交到版本控制
- 🔄 定期轮换密钥和密码

### **开发环境**
- **前端开发**: `cd web && npm run dev`
- **后端开发**: `go run main.go`
- **数据库**: 使用 Docker 容器

### **生产环境**
- **容器化部署**: Docker Compose
- **反向代理**: Nginx
- **SSL 终止**: Let's Encrypt
- **监控**: 健康检查和日志

## 📊 **监控和日志**

### **服务监控**
```bash
# 检查所有服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service_name]

# 健康检查
curl https://liangliangdamowang.edu.deal/api/status
```

### **性能监控**
- **资源使用**: `docker stats`
- **磁盘空间**: 定期清理日志
- **数据库性能**: MySQL 慢查询日志
- **网络延迟**: nginx 访问日志分析

### **日志轮转**
- **自动清理**: 配置 logrotate
- **备份策略**: 重要日志定期备份
- **监控告警**: 关键错误日志监控

## 🔧 **高级配置**

### **负载均衡**
如需扩展到多实例：
```yaml
# docker-compose.yml 扩展示例
new-api-1:
  image: calciumion/new-api:latest
  # ... 配置

new-api-2:
  image: calciumion/new-api:latest
  # ... 配置
```

### **数据库优化**
- **连接池**: 配置合适的连接数
- **缓存**: Redis 缓存策略
- **索引**: 数据库索引优化
- **备份**: 增量备份策略

### **CDN 集成**
- **静态资源**: 可配置 CDN 加速
- **图片优化**: 支持图片压缩和格式转换
- **缓存策略**: 浏览器和 CDN 缓存配置

## 🚨 **应急处理**

### **服务恢复**
```bash
# 快速重启所有服务
docker-compose restart

# 重建并启动
docker-compose up --build -d

# 从备份恢复数据库
./restore_database.sh backup/latest_backup.sql
```

### **配置回滚**
```bash
# 恢复 nginx 配置
cp nginx-newapi/default.conf.backup nginx-newapi/default.conf

# 恢复前端配置
git checkout web/vite.config.js web/src/index.js

# 重新构建
./update-with-ai-path.sh
```

### **数据恢复**
- **数据库**: 从 `./backup/` 目录恢复
- **配置文件**: 从备份目录恢复
- **SSL 证书**: 重新申请或从备份恢复

## 📈 **扩展功能**

### **已集成功能**
- **情侣网站**: `/love` 路径
- **AI 接口网关**: 多模型支持
- **用户管理**: 注册、登录、权限
- **计费系统**: Token 消费统计
- **API 管理**: 渠道和模型配置

### **可扩展功能**
- **监控面板**: Grafana + Prometheus
- **日志分析**: ELK Stack
- **自动扩容**: Kubernetes 部署
- **多地域**: 多数据中心部署

## 🎯 **最佳实践**

### **部署建议**
1. **定期备份**: 每日自动备份数据库
2. **监控告警**: 设置关键指标监控
3. **安全更新**: 定期更新系统和依赖
4. **性能优化**: 根据使用情况调整配置
5. **文档维护**: 记录所有自定义修改

### **运维建议**
1. **版本控制**: 使用 Git 管理配置变更
2. **测试环境**: 重要更新先在测试环境验证
3. **回滚计划**: 每次更新前准备回滚方案
4. **权限管理**: 最小权限原则
5. **日志审计**: 定期审查访问和操作日志

---

## 📝 **更新记录**

### **v1.1 - AI 路径功能** (2025-07-25)
- ✨ 新增 `/ai` 路径前端访问
- ✨ 新增 `/ai/api/*` 和 `/ai/v1/*` API 路径
- ✨ 创建专用更新脚本 `update-with-ai-path.sh`
- ✨ 更新所有 nginx 配置文件
- ✨ 前端构建配置优化
- ✨ 保持向后兼容性

### **v1.0 - 基础部署** (2025-07-24)
- 🚀 Docker Compose 部署
- 🔐 SSL 证书配置
- 📦 数据库备份系统
- 🛠️ 管理脚本集成
- 🌐 域名和反向代理配置

---

## 🚀 **快速参考**

### **新机器部署命令**
```bash
# 一键部署
git clone <repository-url> /root/workspace/new-api
cd /root/workspace/new-api
./deploy-newapi.sh your-domain.com
```

### **常用管理命令**
```bash
# 统一管理
./new-api-manager.sh

# 快速操作
./new-api-manager.sh status    # 查看状态
./new-api-manager.sh backup    # 备份数据
./new-api-manager.sh restart   # 重启服务
./new-api-manager.sh logs      # 查看日志
```

### **访问地址**
- **原始前端**: `https://your-domain.com/`
- **AI前端**: `https://your-domain.com/ai/`
- **原始API**: `https://your-domain.com/api/status`
- **AI API**: `https://your-domain.com/ai/api/status`

### **重要文件位置**
- **部署脚本**: `./deploy-newapi.sh`
- **管理工具**: `./new-api-manager.sh`
- **项目指南**: `./guide-newapi.md`
- **AI路径更新**: `./scripts/maintenance/update-with-ai-path.sh`
- **数据备份**: `./backup/`
- **配置文件**: `./.env`

### **故障排除**
```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 完全重新部署
docker-compose down
./deploy-newapi.sh your-domain.com
```

---

**📞 如需技术支持，请参考本指南的故障排除章节或检查日志文件进行问题诊断。**
