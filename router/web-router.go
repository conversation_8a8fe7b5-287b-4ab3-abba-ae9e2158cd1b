package router

import (
	"embed"
	"github.com/gin-contrib/gzip"
	"github.com/gin-contrib/static"
	"github.com/gin-gonic/gin"
	"net/http"
	"one-api/common"
	"one-api/controller"
	"one-api/middleware"
	"strings"
)

func SetWebRouter(router *gin.Engine, buildFS embed.FS, indexPage []byte) {
	router.Use(gzip.Gzip(gzip.DefaultCompression))
	router.Use(middleware.GlobalWebRateLimit())
	router.Use(middleware.Cache())
	router.Use(static.Serve("/", common.EmbedFolder(buildFS, "web/dist")))
	router.NoRoute(func(c *gin.Context) {
		if strings.HasPrefix(c.Request.RequestURI, "/v1") || strings.HasPrefix(c.Request.RequestURI, "/api") || strings.HasPrefix(c.Request.RequestURI, "/assets") || strings.HasPrefix(c.Request.RequestURI, "/ai") {
			controller.RelayNotFound(c)
			return
		}
		c.<PERSON><PERSON>("Cache-Control", "no-cache")
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexPage)
	})
}

func SetAIWebRouter(router *gin.Engine, buildFS embed.FS, indexPage []byte) {
	aiGroup := router.Group("/ai")
	aiGroup.Use(gzip.Gzip(gzip.DefaultCompression))
	aiGroup.Use(middleware.GlobalWebRateLimit())
	aiGroup.Use(middleware.Cache())
	aiGroup.Use(static.Serve("/ai", common.EmbedFolder(buildFS, "web/dist")))

	// 处理 /ai 根路径
	aiGroup.GET("", func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache")
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexPage)
	})

	// 处理 /ai/ 路径
	aiGroup.GET("/", func(c *gin.Context) {
		c.Header("Cache-Control", "no-cache")
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexPage)
	})

	// 处理 /ai 路径下的所有其他路由（除了 API 路径），返回前端应用
	aiGroup.NoRoute(func(c *gin.Context) {
		// 如果是 API 路径，不处理，让其他路由处理
		if strings.HasPrefix(c.Request.RequestURI, "/ai/api") || strings.HasPrefix(c.Request.RequestURI, "/ai/v1") {
			c.Next()
			return
		}
		c.Header("Cache-Control", "no-cache")
		c.Data(http.StatusOK, "text/html; charset=utf-8", indexPage)
	})
}
