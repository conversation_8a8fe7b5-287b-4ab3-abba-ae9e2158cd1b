import{j as e,E as x}from"./semi-ui-EXX3n6Mn.js";import{r as o}from"./react-core-DskXcPn0.js";import{A as E,s as f}from"./index-DhwdGFnH.js";import{u as b}from"./i18n-C5TZ4d03.js";import{m as j}from"./tools-BUwgD30Q.js";import{I as F,a as y}from"./IllustrationConstruction-BxqVZ4HA.js";import"./react-components-C55tCU1e.js";const S=()=>{const{t}=b(),[r,s]=o.useState(""),[n,i]=o.useState(!1),l=new Date().getFullYear(),c=async()=>{s(localStorage.getItem("about")||"");const h=await E.get("/api/about"),{success:d,message:g,data:u}=h.data;if(d){let a=u;u.startsWith("https://")||(a=j.parse(u)),s(a),localStorage.setItem("about",a)}else f(g),s(t("加载关于内容失败..."));i(!0)};o.useEffect(()=>{c().then()},[]);const m={padding:"24px"},p=e.jsxs("div",{style:{textAlign:"center"},children:[e.jsx("p",{children:t("可在设置页面设置关于内容，支持 HTML & Markdown")}),t("New API项目仓库地址："),e.jsx("a",{href:"https://github.com/QuantumNous/new-api",target:"_blank",rel:"noopener noreferrer",className:"!text-semi-color-primary",children:"https://github.com/QuantumNous/new-api"}),e.jsxs("p",{children:[e.jsx("a",{href:"https://github.com/QuantumNous/new-api",target:"_blank",rel:"noopener noreferrer",className:"!text-semi-color-primary",children:"NewAPI"})," ",t("© {{currentYear}}",{currentYear:l})," ",e.jsx("a",{href:"https://github.com/QuantumNous",target:"_blank",rel:"noopener noreferrer",className:"!text-semi-color-primary",children:"QuantumNous"})," ",t("| 基于")," ",e.jsx("a",{href:"https://github.com/songquanpeng/one-api/releases/tag/v0.5.4",target:"_blank",rel:"noopener noreferrer",className:"!text-semi-color-primary",children:"One API v0.5.4"})," © 2023 ",e.jsx("a",{href:"https://github.com/songquanpeng",target:"_blank",rel:"noopener noreferrer",className:"!text-semi-color-primary",children:"JustSong"})]}),e.jsxs("p",{children:[t("本项目根据"),e.jsx("a",{href:"https://github.com/songquanpeng/one-api/blob/v0.5.4/LICENSE",target:"_blank",rel:"noopener noreferrer",className:"!text-semi-color-primary",children:t("MIT许可证")}),t("授权，需在遵守"),e.jsx("a",{href:"https://github.com/QuantumNous/new-api/blob/main/LICENSE",target:"_blank",rel:"noopener noreferrer",className:"!text-semi-color-primary",children:t("Apache-2.0协议")}),t("的前提下使用。")]})]});return e.jsx("div",{className:"mt-[64px] px-2",children:n&&r===""?e.jsx("div",{className:"flex justify-center items-center h-screen p-8",children:e.jsx(x,{image:e.jsx(y,{style:{width:150,height:150}}),darkModeImage:e.jsx(F,{style:{width:150,height:150}}),description:t("管理员暂时未设置任何关于内容"),style:m,children:p})}):e.jsx(e.Fragment,{children:r.startsWith("https://")?e.jsx("iframe",{src:r,style:{width:"100%",height:"100vh",border:"none"}}):e.jsx("div",{style:{fontSize:"larger"},dangerouslySetInnerHTML:{__html:r}})})})};export{S as default};
