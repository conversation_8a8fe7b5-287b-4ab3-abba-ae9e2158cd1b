import{s as r,b as e,a,S as i}from"./chunk-OW32GOEJ-yfg9id_w.js";import{_ as s}from"./index-DhwdGFnH.js";import"./chunk-BFAMUDN2-DwSMtenG.js";import"./chunk-SKB7J2MH-Qp1Mlwrm.js";import"./semi-ui-EXX3n6Mn.js";import"./react-core-DskXcPn0.js";import"./tools-BUwgD30Q.js";import"./react-components-C55tCU1e.js";import"./i18n-C5TZ4d03.js";var f={parser:a,get db(){return new i(2)},renderer:e,styles:r,init:s(t=>{t.state||(t.state={}),t.state.arrowMarkerAbsolute=t.arrowMarkerAbsolute},"init")};export{f as diagram};
