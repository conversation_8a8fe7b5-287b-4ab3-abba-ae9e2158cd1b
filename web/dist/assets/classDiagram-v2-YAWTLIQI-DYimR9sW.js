import{s as a,c as s,a as e,C as t}from"./chunk-SZ463SBG-X6v4VMfO.js";import{_ as i}from"./index-DhwdGFnH.js";import"./chunk-E2GYISFI-Db3G5k3G.js";import"./chunk-BFAMUDN2-DwSMtenG.js";import"./chunk-SKB7J2MH-Qp1Mlwrm.js";import"./semi-ui-EXX3n6Mn.js";import"./react-core-DskXcPn0.js";import"./tools-BUwgD30Q.js";import"./react-components-C55tCU1e.js";import"./i18n-C5TZ4d03.js";var c={parser:e,get db(){return new t},renderer:s,styles:a,init:i(r=>{r.class||(r.class={}),r.class.arrowMarkerAbsolute=r.arrowMarkerAbsolute},"init")};export{c as diagram};
