[Unit]
Description=Nginx Proxy for New-API
Requires=docker.service
After=docker.service new-api.service
Wants=network-online.target
After=network-online.target

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/root/new-api
ExecStart=/usr/bin/docker run -d --name nginx-proxy --network new-api_default -p 80:80 -p 443:443 -v /root/new-api/nginx/default.conf:/etc/nginx/conf.d/default.conf -v /root/new-api/nginx/ssl:/etc/nginx/ssl nginx:latest
ExecStop=/usr/bin/docker stop nginx-proxy
ExecStopPost=/usr/bin/docker rm nginx-proxy
TimeoutStartSec=0
User=root
Group=root

[Install]
WantedBy=multi-user.target
