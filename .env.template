# New-API Environment Configuration Template
# Copy this file to .env and modify the values according to your setup

# =============================================================================
# BASIC CONFIGURATION
# =============================================================================

# Base URL for the application (leave empty for auto-detection)
BASE_URL=

# Timezone setting
TZ=Asia/Shanghai

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MySQL Database Configuration
# IMPORTANT: Change this to a secure password for production!
MYSQL_ROOT_PASSWORD=NewAPI_2024_SecurePass
MYSQL_DATABASE=new-api

# Database connection string for the application
# Format: username:password@tcp(host:port)/database
# This will be automatically constructed using the above values

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================

# Redis connection string
REDIS_CONN_STRING=redis://redis

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Enable error logging
ERROR_LOG_ENABLED=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Session secret for multi-machine deployment
# IMPORTANT: Change this to a random string for production!
SESSION_SECRET=NewAPI_Session_Secret_2024_ChangeMe

# =============================================================================
# MULTI-NODE DEPLOYMENT (Optional)
# =============================================================================

# Uncomment and configure for multi-node deployment

# Node type: master or slave
# NODE_TYPE=slave

# Database sync frequency (in seconds)
# SYNC_FREQUENCY=60

# Frontend base URL for multi-node deployment
# FRONTEND_BASE_URL=https://your-domain.com

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================

# Your domain name (used for SSL certificate generation)
DOMAIN_NAME=your-domain.com

# API subdomain (optional)
API_DOMAIN=api.your-domain.com

# =============================================================================
# SSL CONFIGURATION
# =============================================================================

# Email for Let's Encrypt SSL certificate
SSL_EMAIL=<EMAIL>

# =============================================================================
# ADVANCED CONFIGURATION (Optional)
# =============================================================================

# Tiktoken cache directory (uncomment if needed)
# TIKTOKEN_CACHE_DIR=./tiktoken_cache

# Custom configuration for specific features
# Add your custom environment variables here

# =============================================================================
# NOTES
# =============================================================================
# 
# 1. Make sure to change MYSQL_ROOT_PASSWORD to a secure password
# 2. Set SESSION_SECRET to a random string for production
# 3. Configure DOMAIN_NAME for SSL certificate generation
# 4. Update SSL_EMAIL with your actual email address
# 5. For multi-node deployment, uncomment and configure the relevant sections
#
